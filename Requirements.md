Pole Vault Log – iPhone/iPad App Requirements • Master Spec (v11)

────────────────────────────────────────────────────────────────────
1 • Product Vision
A sleek, lightning-fast logbook for pole vaulters that captures every jump,
technical detail, note, and video in an intuitive grid; always displays both
metric & imperial units (preferred unit first and bold); lets athletes fine-tune
measurement ranges and choose official meet standards; visualises progress;
and supports full data ownership and export.

────────────────────────────────────────────────────────────────────
2 • Data Model  (Core Data — all linear distances persisted in **centimeters**)

Entity   | Key Fields (camel-case)                                                                       | Notes
---------|-----------------------------------------------------------------------------------------------|-----------------------------------------------------
Athlete  | id, name, dateOfBirth, dominantHand, personalBestCm, personalBestIn                           | —
Session  | id, date, type (practice / meet), title, location, weather, heightStandard, notesAthlete,<br>notesCoach, bestHeightCm, bestHeightIn, gridHeightsCm [Double], videoLocalIds [String] | **heightStandard** = "Free-Range" (practice) | "NFHS" | "FHSAA" | "Custom"
Jump     | id, sessionId, columnIndex, attemptIndex (1-3), resultCode (“O”/“X”/“P”),<br>barHeightCm, runStartCm, handHoldCm, takeOffStepCm, standardCm, comment, videoLocalId | barHeightCm duplicated for fast queries

Videos are referenced by Photo-Library **PHAsset local IDs** only (no file copies).

────────────────────────────────────────────────────────────────────
3 • Core UI & Workflow

3.1 Home  
• “New Session” card → dialog: choose Practice or Meet.  
• For **Meet**, an extra drop-down **Height Standard** (default **NFHS**;
  alternatives **FHSAA** or any user-defined Custom table).  
• History cards show mini heat-map thumbnails.

3.2 Session Screen  

Grid (newest column always far **left**)  
• Top-left “+” opens a **Height Picker** drop-down:  

  – **Practice (Free-Range)**: values from **0.25 m to 7.00 m in 0.01 m steps**  
    (¼-inch equivalent).  
  – **Meet**: values come from the session’s selected standard list  
    (e.g. NFHS progression).  

• The picker lists **both units in one line**: preferred first (bold) · secondary
  light, e.g. `2.55 m • 8' 4 ½" ` or `8' 4 ½" • 2.55 m`.  
• When the user confirms a height, the next “+” pre-selects **the next higher
  value in the same list** (not simply +1 cm / +¼").  
• Validation: chosen height must exceed current leftmost height.  
• Column header editable via long-press; height fixed for its three attempts.  
• Rows: Jump 1, Jump 2, Jump 3.  
• Tap a cell → Quick-Form Sheet.

Jump Summary (beneath grid) lists latest jumps; every technical value shows
both units with the preferred unit bold.

3.3 Quick-Form Sheet (fields always visible)  
1. Result selector **O X P** (P hidden if this column already contains O)  
2. Run Start — stepper + text (within user-set range; preferred bold)  
3. Hand Hold  
4. Take-Off Step  
5. Standard  
6. Attach from Photos  
7. Comment  
8. Save & Next • Cancel  
Defaults pull from most-recent saved jump.

────────────────────────────────────────────────────────────────────
4 • Result Logic

• **Make (O)** → greys remaining attempts in that column (“–”) & resets miss counter.  
• **Pass (P)** → allowed on any attempt until a Make in that column; selecting P greys remaining attempts; miss counter unchanged.  
• **Miss Counter** → +1 on X, unchanged on P, reset on O; OUT after three consecutive X values across heights (grid shows “OUT”).  

────────────────────────────────────────────────────────────────────
5 • Analytics Dashboard (Swift Charts, both units shown)

Metric                                 | Notes (preferred unit bold)
-------------------------------------- | --------------------------------------------------------------
Best-height line chart                 | Highest cleared height per session
Clearance efficiency                   | makes ÷ (makes + misses) – last 30 days
Average run start / standard           | Mean of successful jumps
Personal-best badge                    | Confetti + haptic on new PB
Filter chips                           | Date range • practice/meet • location • heightStandard

────────────────────────────────────────────────────────────────────
6 • Data Interoperability

Export single session  → .pvl.json (+ .xlsx) via share sheet  
Export all sessions    → ZIP: master .pvl.json + .csv & .xlsx  
Import                 → UIDocumentPicker (.pvl.json → Core Data)  
Videos                 → optional iCloud links via PHSharingController

────────────────────────────────────────────────────────────────────
7 • Settings

7.1 Units  
Preferred unit (cm/m ↔ ft/in) — UI reorders/emboldens accordingly.

7.2 **Practice-Range Height Picker**  
Min (default 0.25 m / 10″), Max (default 7 m / 23′), Step (default 0.01 m ≈ ¼″).

7.3 **Meet Standards Manager**  
• Built-in tables: **NFHS** (default) and **FHSAA**.  
• “Custom Standard” → import CSV or manual list editor.  
• User can rename, disable, or set a custom standard as default for new meets.

7.4 Custom Range Editor  
Min/Max for Run Start, Hand Hold, Take-Off Step, Standard, Bar Height
(both units displayed; controls obey limits).

Other settings unchanged (quick-height presets, iCloud sync, Face ID/Touch ID lock, video cache alert).

────────────────────────────────────────────────────────────────────
8 • Design & UX  (key points)

• Every height/measurement displays **both units** on all screens.  
• Preferred unit is **first and bold**; secondary is light.  
• Height Picker uses single scroll list (no segmented control).

Remaining UX details (style, gestures, animations, haptics, accessibility,
on-boarding) are unchanged from v9.

────────────────────────────────────────────────────────────────────
9 • Architecture & Tech  (unchanged)

SwiftUI • MVVM • Combine • Core Data + CloudKit • Codable import/export •
PhotosPicker / PHSharingController • FileExporter / FileImporter • XCTest / XCUITest

────────────────────────────────────────────────────────────────────
10 • Non-Functional  (unchanged)

Launch <1 s • 60 fps scrolling • Offline-first • Dark Mode • Split-View •
Stage Manager • Localization-ready (en, es)

────────────────────────────────────────────────────────────────────
11 • Backlog Ideas  (unchanged)

watchOS companion • Bluetooth laser height reader • AI pose scoring • Team workspace


### FHSAA Prescribed Pole‑Vault Heights  
*(Florida High School Athletic Association 2023‑24 Track & Field Sport Manual §4.14.2.2.4 (b)(6))*  [oai_citation:0‡Amazon Web Services, Inc.](https://s3.amazonaws.com/fhsaa.org/documents/2023/11/29/2324_track_sport_manual.pdf)

| Order | Height (meters) | Height (feet & inches) |
|:----:|:---------------:|:-----------------------:|
| 1 | **1.50 m** | **4′ 11″** |
| 2 | 1.65 m | 5′ 5″ |
| 3 | 1.80 m | 5′ 10 ¾″ |
| 4 | 1.95 m | 6′ 4 ¾″ |
| 5 | 2.10 m | 6′ 10 ¾″ |
| 6 | 2.25 m | 7′ 4 ½″ |
| 7 | 2.40 m | 7′ 10 ½″ |
| 8 | 2.55 m | 8′ 4 ½″ |
| 9 | 2.70 m | 8′ 10 ¼″ |
| 10 | 2.85 m | 9′ 4 ¼″ |
| 11 | 3.00 m | 9′ 10″ |
| 12 | 3.15 m | 10′ 4″ |
| 13 | 3.30 m | 10′ 10″ |
| 14 | 3.45 m | 11′ 3 ¾″ |
| 15 | 3.60 m | 11′ 9 ¾″ |
| 16 | 3.75 m | 12′ 3 ¾″ |
| 17 | 3.90 m | 12′ 9 ½″ |
| 18 | 4.05 m | 13′ 3 ½″ |
| 19 | 4.20 m | 13′ 9 ¼″ |
| 20 | 4.35 m | 14′ 3 ¼″ |
| 21 | 4.50 m | 14′ 9 ¼″ |
| 22 | 4.65 m | 15′ 3″ |
| 23 | 4.80 m | 15′ 9″ |
| 24 | 4.95 m | 16′ 3″ |

> *Notes*  
> • These heights represent the mandatory increment progression for FHSAA‑sanctioned district, regional, and state meets.  
> • Metric is the official measurement system; feet‑inch conversions are rounded to the nearest ¼‑inch to match NFHS convention.  
> • For the app, load this list as the default **“NFHS/FHSAA Standard”** table for “Meet” sessions.  


### NFHS Pole‑Vault Height Guidance

The **National Federation of State High School Associations (NFHS)** **does not publish a fixed table of bar heights** the way some state associations (e.g., FHSAA) do.  Instead, the NFHS Track & Field Rules Book leaves the starting height and all successive heights to the meet’s **Games Committee**, with only two universal constraints:  [oai_citation:0‡E.S.H.S. XC & TRACK](https://eshsxctf.weebly.com/uploads/2/8/4/2/2842129/pole-vault-rules-comparison-full-mar2012.pdf?utm_source=chatgpt.com)

| Rule Extract | Practical Implication in Meets |
|--------------|--------------------------------|
| “*Starting height & successive heights are chosen by the Games Committee … The **recommended** initial increment is **15 cm** (≈ 6″) and shall **never be less than 5 cm**.*” | Meets typically pick an opening height suited to the field and raise the bar **6 inches (≈ 15 cm)** each round until the field thins; some switch to **3 inch (≈ 8 cm)** or **4 inch (≈ 10 cm)** increments at higher levels. |
| “*The increment shall never increase during the competition.*” | Once an increment is chosen (e.g., 6″ or 10 cm) it must remain constant until a winner is determined. |
| “*The winner may choose any progression after all other athletes are eliminated.*” | A lone remaining vaulter may request alternative heights to chase records. |

#### What this means for the app

1. **Meet Mode (NFHS Standard)**
   * Offer a **Games‑Committee Picker**:  
     *Default increments:*  
       * 15 cm / 6″ (recommended)  
       * 10 cm / 4″  
       * 8 cm / 3″  
     *Starting height* free‑entry with validation to align to the chosen increment grid.
   * Height drop‑down then auto‑generates the progression list on‑the‑fly:  
     *startHeight, startHeight + increment, startHeight + 2·increment, …*  
   * The “+” default always selects the **next value** in this dynamic list.

2. **Custom Standards**
   * States that do prescribe exact tables (FHSAA, UIL‑TX, etc.) can still be loaded as fixed lists in **Settings › Meet Standards**.

3. **Display Format**
   * Each generated height shows both units with the user’s preferred unit bold, e.g.  
     *Metric primary:* `3.15 m • 10′ 4″`  
     *Imperial primary:* `10′ 4″ • 3.15 m`

> **Key takeaway:** Unlike FHSAA, **NFHS meets require a flexible progression, not a single nationwide table.**  The app should therefore build the height list at meet setup using the Games‑Committee starting height and chosen increment, while still supporting fixed tables for associations that mandate them.


PoleVault Log Pro – Media Handling Addendum v11‑media‑2
(this file EXTENDS Master Spec v11; all un‑mentioned sections remain unchanged)

────────────────────────────────────────────────────────
0 • Purpose
Add complete, Apple‑compliant rules for picking, copying, thumbnailing,
playing, and caching photos / videos attached to any Jump.

────────────────────────────────────────────────────────
1 • UI Extensions

1.1 Quick‑Form
• “Attach Media” button opens PHPicker (filter videos+images, limit 6).

1.2 Jump Grid & Summary
• Each jump shows a 60×60 thumbnail:
    – first video poster frame
    – else first photo
    – else gray camera glyph
• Tap thumbnail → Media Lightbox
    – horizontal pager
    – AVPlayer inline for video
    – pinch‑zoom for photos
    – share button (UIActivityViewController)

1.3 Converter / Dashboard pages
• If a jump has any attachment, small clip badge appears next to height.

────────────────────────────────────────────────────────
2 • Data Model Changes (Core Data / CloudKit)

Entity  : New field           : Type
Jump    : mediaItems          : Transformable (NSArray<JumpMedia>)

JumpMedia struct (Codable)
    id               UUID
    type             String   // "video" | "photo"
    assetIdentifier  String?  // PHAsset local ID
    fileURL          URL?     // sandbox copy if no asset ID or user opts‑in
    posterTime       Double?  // seconds for thumbnail (default 0.5)

Lightweight migration; no schema version bump needed in CloudKit.

────────────────────────────────────────────────────────
3 • Media Storage Policy

• Default: store only assetIdentifier.
• Fallback: if assetIdentifier is nil ➜ copy file to
  ~/Library/Application Support/Media/{uuid}.{ext}
  set NSURLIsExcludedFromBackupKey = true.
• User setting “Always copy picked media” forces copy even when ID exists.
• Thumbnail JPEG cached at
  ~/Library/Caches/Thumbs/{uuid}.jpg  (max edge 200 px).

────────────────────────────────────────────────────────
4 • Retrieval & Playback (best practice)

On attach:
    if item.assetIdentifier != nil
        save JumpMedia(assetID, nil)
    else
        urlTemp = await item.loadTransferable(type: URL.self)
        copy ▸ targetURL
        save JumpMedia(nil, targetURL)

On playback:
    if assetIdentifier != nil
        fetch PHAsset → requestPlayerItem / requestImageData
    else
        load from fileURL

Thumbnail generation:
    for video ➜ generateCGImagesAsynchronously at posterTime
    for photo ➜ downscale longestEdge 200 px, save JPEG to /Thumbs

────────────────────────────────────────────────────────
5 • Entitlements & Capabilities

Info.plist keys
    NSPhotoLibraryUsageDescription          = "Save and load jump videos."
    NSPhotoLibraryAddUsageDescription       = "Attach slow‑motion videos."
Entitlements
    com.apple.developer.user-photos         = read-write
Signing & Capabilities
    Photos Library ► Access Level ► Read and Write

────────────────────────────────────────────────────────
6 • Settings Additions

Settings ▸ Media
    • Always copy picked media              (toggle, default OFF)
    • Max attachments per jump              (stepper, 1–10, default 6)
    • Video poster time default             (slider 0–3 s, default 0.5 s)
    • Video cache warning threshold         (slider 0.5–5 GB, default 2 GB)
    • Clear local copies button             (shows current MB, empties /Media)

────────────────────────────────────────────────────────
7 • Export / Import

• .pvl.json JumpMedia block
    { "id":"...", "type":"video", "assetIdentifier":"opt",
      "fileName":"opt_local.mov", "posterTime":0.5 }
• If fileURL exists ➜ include the file inside export ZIP next to JSON.
• On import: if file exists ➜ copy to sandbox, else leave assetIdentifier only.

────────────────────────────────────────────────────────
8 • Background Housekeeping

• Observe PHPhotoLibrary changes:
    on asset delete ➜ if matching assetID, set assetIdentifier = nil,
    copy asset to sandbox if not already.
• Daily task clears /Thumbs older than 30 days.

────────────────────────────────────────────────────────
9 • Validation Checklist

[ ] Photos capability read‑write on target
[ ] Entitlement file included in provisioning profile
[ ] All thumbnails generated <100 KB each
[ ] Clean archive passes App Store Transporter without ITMS‑90xxx icon or
    entitlement errors