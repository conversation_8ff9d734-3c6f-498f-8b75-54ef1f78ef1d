import SwiftUI
import CloudKit

struct LogView: View {
    @Environment(\.managedObjectContext) private var viewContext

    @FetchRequest private var sessions: FetchedResults<Session>

    @State private var isShowingNewSession = false
    @State private var showingDeleteAlert = false
    @State private var sessionsToDelete: IndexSet?
    @State private var hasMediaToDelete = false
    @State private var refreshID = UUID() // State to force refresh when media files change
    @State private var isSyncing = false // State to track manual sync in progress
    @State private var syncError: Error? // State to track sync errors

    // Listen for data reset and CloudKit sync notifications
    init() {
        // We need to use a different approach for struct views
        // The notification will be handled by the parent view controller refreshing the view

        // Initialize the fetch request using the helper
        _sessions = FetchRequest(fetchRequest: CoreDataFetchRequestHelper.createSessionFetchRequest(
            context: PersistenceController.shared.container.viewContext,
            sortDescriptors: [NSSortDescriptor(keyPath: \Session.date, ascending: false)]
        ))
    }

    // Setup notification observers when the view appears
    private func setupNotificationObservers() {
        // Listen for CloudKit data changes
        NotificationCenter.default.addObserver(
            forName: CloudKitSyncManager.NotificationName.cloudKitDataChanged,
            object: nil,
            queue: .main
        ) { [self] _ in
            print("LogView received CloudKit data change notification")
            refreshID = UUID()
            // Reset syncing state if it was a manual sync
            isSyncing = false
        }

        // Listen for manual sync completion
        NotificationCenter.default.addObserver(
            forName: CloudKitSyncManager.NotificationName.manualSyncCompleted,
            object: nil,
            queue: .main
        ) { [self] _ in
            print("LogView received manual sync completion notification")
            isSyncing = false
            syncError = nil
            refreshID = UUID()
        }

        // Listen for manual sync failures
        NotificationCenter.default.addObserver(
            forName: CloudKitSyncManager.NotificationName.manualSyncFailed,
            object: nil,
            queue: .main
        ) { [self] notification in
            print("LogView received manual sync failure notification")
            isSyncing = false
            syncError = notification.userInfo?["error"] as? Error
            refreshID = UUID()
        }
    }

    /// Performs a manual CloudKit sync operation
    @MainActor
    private func performManualSync() async {
        guard !isSyncing else {
            print("Sync already in progress, ignoring request")
            return
        }

        print("Starting manual CloudKit sync...")
        isSyncing = true
        syncError = nil

        // First, try to run the diagnostics to check for cloud-only records
        let diagnosticsResult = await runCloudKitDiagnostics()
        if diagnosticsResult.hasCloudOnlyRecords {
            print("📊 Diagnostics detected cloud-only records that need to be synced")
        }

        // Perform the manual sync
        let result = await PersistenceController.shared.manualCloudKitSync()

        if !result.success {
            syncError = result.error
            print("Manual sync failed: \(result.error?.localizedDescription ?? "Unknown error")")

            // Show error message briefly
            withAnimation {
                isSyncing = false
            }

            // Display a toast message for errors
            var errorMessage = "Sync failed"
            if let error = result.error {
                // Provide a more user-friendly error message
                if (error as NSError).domain == "com.brummblebee.pvlogpro" && (error as NSError).code == -1 {
                    errorMessage = "Sync timed out - try again later"
                } else {
                    errorMessage = "Sync failed: \(error.localizedDescription)"
                }
            }
            showToast(message: errorMessage)
        } else {
            print("Manual sync completed successfully")

            // If we had cloud-only records before, run diagnostics again to verify they were synced
            if diagnosticsResult.hasCloudOnlyRecords {
                let postSyncDiagnostics = await runCloudKitDiagnostics()
                if postSyncDiagnostics.hasCloudOnlyRecords {
                    print("⚠️ Some cloud-only records still exist after sync")
                } else {
                    print("✅ All cloud-only records were successfully synced")
                }
            }

            // Hide the sync indicator with animation
            withAnimation {
                isSyncing = false
            }

            // Show a brief success message
            showToast(message: "iCloud sync complete")
        }
    }

    /// Runs CloudKit diagnostics to check for cloud-only records
    private func runCloudKitDiagnostics() async -> (hasCloudOnlyRecords: Bool, entityTypes: [String]) {
        print("📊 Running CloudKit diagnostics to check for cloud-only records")

        // Create a task to run the diagnostics
        return await withCheckedContinuation { continuation in
            // Use the CloudKitDiagnosticManager to check for cloud-only records
            CloudKitDiagnosticManager.shared.checkForCloudOnlyRecords { entityTypes, error in
                if let error = error {
                    print("❌ Error running CloudKit diagnostics: \(error.localizedDescription)")
                    continuation.resume(returning: (false, []))
                    return
                }

                let hasCloudOnlyRecords = !entityTypes.isEmpty
                if hasCloudOnlyRecords {
                    print("📊 Found cloud-only records for entity types: \(entityTypes.joined(separator: ", "))")
                } else {
                    print("📊 No cloud-only records found")
                }

                continuation.resume(returning: (hasCloudOnlyRecords, entityTypes))
            }
        }
    }

    /// Shows a toast message that automatically disappears after a few seconds
    private func showToast(message: String) {
        // Provide haptic feedback
        let feedbackGenerator = UINotificationFeedbackGenerator()

        if message.contains("failed") || message.contains("error") {
            feedbackGenerator.notificationOccurred(.error)
        } else {
            feedbackGenerator.notificationOccurred(.success)
        }

        // Create and configure the toast view
        let toastView = UIView()
        toastView.backgroundColor = UIColor.systemGray5
        toastView.alpha = 0.0
        toastView.layer.cornerRadius = 16
        toastView.clipsToBounds = true
        toastView.layer.shadowColor = UIColor.black.cgColor
        toastView.layer.shadowOffset = CGSize(width: 0, height: 2)
        toastView.layer.shadowOpacity = 0.2
        toastView.layer.shadowRadius = 4

        // Create a stack view for icon and text
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.alignment = .center
        stackView.spacing = 8

        // Add appropriate icon
        let iconView = UIImageView()
        if message.contains("failed") || message.contains("error") {
            iconView.image = UIImage(systemName: "xmark.circle.fill")
            iconView.tintColor = UIColor.systemRed
        } else {
            iconView.image = UIImage(systemName: "checkmark.circle.fill")
            iconView.tintColor = UIColor.systemGreen
        }
        iconView.contentMode = .scaleAspectFit

        // Configure the label
        let label = UILabel()
        label.text = message
        label.textColor = UIColor.label
        label.font = UIFont.systemFont(ofSize: 15, weight: .medium)
        label.numberOfLines = 0

        // Add views to stack
        stackView.addArrangedSubview(iconView)
        stackView.addArrangedSubview(label)

        // Add the stack view to the toast view
        toastView.addSubview(stackView)
        stackView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            iconView.widthAnchor.constraint(equalToConstant: 24),
            iconView.heightAnchor.constraint(equalToConstant: 24),
            stackView.leadingAnchor.constraint(equalTo: toastView.leadingAnchor, constant: 16),
            stackView.trailingAnchor.constraint(equalTo: toastView.trailingAnchor, constant: -16),
            stackView.topAnchor.constraint(equalTo: toastView.topAnchor, constant: 12),
            stackView.bottomAnchor.constraint(equalTo: toastView.bottomAnchor, constant: -12)
        ])

        // Add the toast view to the key window
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first else {
            return
        }

        window.addSubview(toastView)
        toastView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            toastView.centerXAnchor.constraint(equalTo: window.centerXAnchor),
            toastView.topAnchor.constraint(equalTo: window.safeAreaLayoutGuide.topAnchor, constant: 50),
            toastView.widthAnchor.constraint(lessThanOrEqualTo: window.widthAnchor, constant: -32)
        ])

        // Animate the toast in and out
        UIView.animate(withDuration: 0.3, animations: {
            toastView.alpha = 1.0
        }, completion: { _ in
            // After showing for 2 seconds, fade out
            DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                UIView.animate(withDuration: 0.3, animations: {
                    toastView.alpha = 0.0
                }, completion: { _ in
                    toastView.removeFromSuperview()
                })
            }
        })
    }

    var body: some View {
        NavigationStack {
            ZStack {
                // Background watermark
                WatermarkView()

                if sessions.isEmpty {
                    // Empty state with ScrollView
                    ScrollView {
                        VStack(spacing: 20) {
                            PoleVaultIcon(size: CGSize(width: 80, height: 80), foregroundColor: AppTheme.accentColor)

                            Text("No Sessions Yet")
                                .font(.title2)
                                .fontWeight(.bold)

                            Text("Tap the + button to create your first session")
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)

                            Button(action: {
                                isShowingNewSession = true
                            }) {
                                Label("New Session", systemImage: "plus")
                                    .padding()
                                    .background(AppTheme.accentColor)
                                    .foregroundColor(.white)
                                    .cornerRadius(AppTheme.cornerRadius)
                            }
                            .padding(.top)
                        }
                        .padding(.top, 40)
                        .padding(.horizontal)
                    }
                } else {
                    // Session list with pull-to-refresh
                    List {
                        ForEach(sessions) { session in
                            NavigationLink(destination: SessionDetailView(session: session)) {
                                SessionRowView(session: session)
                            }
                        }
                        .onDelete(perform: deleteSessions)
                    }
                    .overlay(alignment: .top) {
                        // Show a circular loading indicator when syncing
                        if isSyncing {
                            HStack(spacing: 10) {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle())
                                    .scaleEffect(1.0)

                                Text("Syncing with iCloud...")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            .padding(10)
                            .background(Color(UIColor.systemBackground).opacity(0.9))
                            .cornerRadius(10)
                            .shadow(radius: 2)
                            .padding(.top, 10)
                            .transition(.opacity)
                            .animation(.easeInOut, value: isSyncing)
                        }
                    }

                }

                // Floating action button for non-empty state
                if !sessions.isEmpty {
                    VStack {
                        Spacer()
                        HStack {
                            Spacer()
                            Button(action: {
                                isShowingNewSession = true
                            }) {
                                Image(systemName: "plus")
                                    .font(.title)
                                    .foregroundColor(.white)
                                    .frame(width: 60, height: 60)
                                    .background(AppTheme.accentColor)
                                    .clipShape(Circle())
                                    .shadow(radius: 4)
                            }
                            .padding()
                        }
                    }
                }
            }
            .navigationTitle("Log")
            .sheet(isPresented: $isShowingNewSession) {
                NewSessionView()
            }
            .toolbar {
                if !sessions.isEmpty {
                    // Edit button in the trailing position
                    ToolbarItem(placement: .primaryAction) {
                        EditButton()
                    }
                }
            }
            .id(refreshID) // Force refresh when refreshID changes
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("MediaFilesChanged"))) { _ in
                // Refresh the view when media files are changed
                print("Refreshing LogView due to media files change")
                refreshID = UUID() // Force a refresh
            }
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("RefreshFetchRequests"))) { _ in
                // Refresh the view when fetch requests need to be refreshed
                print("Refreshing LogView due to fetch request refresh")
                refreshID = UUID() // Force a refresh
            }
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("CoreDataContextReset"))) { _ in
                // Refresh the view when the Core Data context is reset
                print("Refreshing LogView due to Core Data context reset")

                // Force a refresh of the view
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    refreshID = UUID()
                }
            }
            .onAppear {
                setupNotificationObservers()
            }
            .alert(isPresented: $showingDeleteAlert) {
                if hasMediaToDelete {
                    return Alert(
                        title: Text("Delete Session"),
                        message: Text("This session contains photos or videos. The media references will be removed from the app, and any local copies stored within the app will be deleted. The original files in your Photos library or iCloud will remain untouched. This action cannot be undone."),
                        primaryButton: .destructive(Text("Delete")) {
                            confirmDeleteSessions()
                        },
                        secondaryButton: .cancel()
                    )
                } else {
                    return Alert(
                        title: Text("Delete Session"),
                        message: Text("Are you sure you want to delete this session? This action cannot be undone."),
                        primaryButton: .destructive(Text("Delete")) {
                            confirmDeleteSessions()
                        },
                        secondaryButton: .cancel()
                    )
                }
            }
        }
    }

    private func deleteSessions(at offsets: IndexSet) {
        // Store the offsets for later use
        sessionsToDelete = offsets

        // Check if any of the sessions to delete have media attachments
        hasMediaToDelete = false
        for index in offsets {
            if sessions[index].hasMediaAttachments() {
                hasMediaToDelete = true
                break
            }
        }

        // Show confirmation alert
        showingDeleteAlert = true
    }

    private func confirmDeleteSessions() {
        guard let offsets = sessionsToDelete else { return }

        // First, collect all media items from sessions to be deleted
        var mediaItemsToDelete: [JumpMediaStruct] = []

        print("Preparing to delete \(offsets.count) sessions")

        for index in offsets {
            let session = sessions[index]
            print("Processing session: \(session.title ?? "Untitled") (ID: \(session.id ?? "unknown"))")

            // Get all jumps for this session
            if let jumps = session.jumps?.allObjects as? [Jump] {
                print("Session has \(jumps.count) jumps")

                // Collect all media items from all jumps
                for jump in jumps {
                    let mediaItems = jump.getMediaItems()
                    print("Jump (ID: \(jump.id ?? "unknown")) has \(mediaItems.count) media items")

                    if !mediaItems.isEmpty {
                        for media in mediaItems {
                            print("Found media: type=\(media.type), id=\(media.id), assetID=\(media.assetIdentifier ?? "nil")")
                        }
                        mediaItemsToDelete.append(contentsOf: mediaItems)
                    }
                }
            }
        }

        print("Total media items to delete: \(mediaItemsToDelete.count)")

        // Now delete the sessions (this will cascade delete the jumps)
        withAnimation {
            offsets.map { sessions[$0] }.forEach(viewContext.delete)

            do {
                try viewContext.save()
                print("Successfully deleted sessions from Core Data")

                // We no longer need to delete local media files as we only use asset identifiers
                if !mediaItemsToDelete.isEmpty {
                    print("Media items will be automatically cleaned up by Core Data cascade delete")
                }

            } catch {
                let nsError = error as NSError
                print("Error deleting sessions: \(nsError), \(nsError.userInfo)")
            }
        }

        // Reset state
        sessionsToDelete = nil
    }
}

// Session row component
struct SessionRowView: View {
    @State private var refreshID = UUID() // State to force refresh when media files change
    @ObservedObject var session: Session

    @FetchRequest private var jumps: FetchedResults<Jump>

    init(session: Session) {
        self.session = session

        // Configure the fetch request for jumps using the helper
        _jumps = FetchRequest(fetchRequest: CoreDataFetchRequestHelper.createJumpFetchRequest(
            context: PersistenceController.shared.container.viewContext,
            sortDescriptors: [NSSortDescriptor(keyPath: \Jump.order, ascending: true)],
            predicate: NSPredicate(format: "session == %@", session)
        ))
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(session.title ?? "Untitled Session")
                    .font(.headline)

                Spacer()

                if session.bestHeightCm > 0 {
                    HStack(spacing: 4) {
                        // Primary unit (based on user preference)
                        Text(bestHeightString)
                            .font(.subheadline)
                            .fontWeight(.medium)

                        // Secondary unit
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        if AppTheme.useMetricSystem {
                            Text(HeightConverter.cmToFeetInchesString(session.bestHeightCm))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        } else {
                            Text(HeightConverter.cmToMetersString(session.bestHeightCm))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(AppTheme.accentColorLight.opacity(0.2))
                    .cornerRadius(8)
                }
            }

            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    HStack(spacing: 6) {
                        // Date
                        // Convert TimeInterval to Date
                        let date = Date(timeIntervalSinceReferenceDate: session.date)
                        Text(DateFormatters.mediumDate.string(from: date))
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        // Type badge
                        if let type = session.type {
                            Text(type)
                                .font(.caption)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(type == "Practice" ? Color.gray.opacity(0.2) : Color.orange.opacity(0.2))
                                .cornerRadius(4)
                        }
                    }

                    // Location if available
                    if let location = session.location, !location.isEmpty {
                        Text(location)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                // Jump statistics and media indicator
                if jumps.count > 0 {
                    HStack(spacing: 8) {
                        // Media indicator
                        if hasMedia {
                            Image(systemName: hasVideo ? "video.fill" : "photo.fill")
                                .foregroundColor(.blue)
                                .font(.caption)
                        }

                        HStack(spacing: 4) {
                            Label("\(jumps.filter { $0.result == "make" }.count)", systemImage: "checkmark.circle.fill")
                                .foregroundColor(.green)
                                .font(.caption)
                                .labelStyle(CompactLabelStyle())

                            Label("\(jumps.filter { $0.result == "miss" }.count)", systemImage: "xmark.circle.fill")
                                .foregroundColor(.red)
                                .font(.caption)
                                .labelStyle(CompactLabelStyle())

                            Label("\(jumps.filter { $0.result == "pass" }.count)", systemImage: "arrow.right.circle.fill")
                                .foregroundColor(.gray)
                                .font(.caption)
                                .labelStyle(CompactLabelStyle())
                        }
                    }
                }
            }
        }
        .padding(.vertical, 4)
        .id(refreshID) // Force refresh when refreshID changes
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("MediaFilesChanged"))) { _ in
            // Refresh the view when media files are changed
            print("Refreshing SessionRowView due to media files change")
            refreshID = UUID() // Force a refresh
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("RefreshFetchRequests"))) { _ in
            // Refresh the view when fetch requests need to be refreshed
            print("Refreshing SessionRowView due to fetch request refresh")
            refreshID = UUID() // Force a refresh
        }
    }

    private var bestHeightString: String {
        if AppTheme.useMetricSystem {
            return HeightConverter.cmToMetersString(session.bestHeightCm)
        } else {
            return HeightConverter.cmToFeetInchesString(session.bestHeightCm)
        }
    }

    // Check if any jump in the session has media attached
    private var hasMedia: Bool {
        for jump in jumps {
            // Check for media items
            let mediaItems = jump.getMediaItems()
            if !mediaItems.isEmpty {
                return true
            }
        }

        return false
    }

    // Check if any jump in the session has a video attached
    private var hasVideo: Bool {
        for jump in jumps {
            // Check for video media items
            let mediaItems = jump.getMediaItems()
            for media in mediaItems {
                if media.type == .video {
                    return true
                }
            }
        }

        return false
    }
}

#Preview {
    let previewController = PersistenceController(inMemory: true)
    let context = previewController.container.viewContext

    do {
        // Create a sample session with jumps for preview
        let session = Session.create(in: context, athlete: Athlete.create(in: context, name: "Test Athlete"), type: "practice")
        session.title = "Practice Session"
        session.location = "School Track"

        // Add some jumps
        _ = Jump.create(in: context, session: session, order: 1, heightCm: 400, result: "make")
        _ = Jump.create(in: context, session: session, order: 2, heightCm: 420, result: "miss")
        _ = Jump.create(in: context, session: session, order: 3, heightCm: 420, result: "make")
        _ = Jump.create(in: context, session: session, order: 4, heightCm: 440, result: "pass")
        _ = Jump.create(in: context, session: session, order: 5, heightCm: 440, result: "miss")

        // Update best height
        session.bestHeightCm = 420
        session.bestHeightIn = 420 / 2.54

        try context.save()
    } catch {
        print("Error creating preview data: \(error)")
    }

    return LogView()
        .environment(\.managedObjectContext, context)
}
