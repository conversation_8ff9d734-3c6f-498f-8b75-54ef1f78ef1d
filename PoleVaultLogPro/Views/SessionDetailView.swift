import SwiftUI

// Context structs for proper sheet presentation
struct NewJumpContext: Identifiable {
    let id = UUID()
    let height: Double
    let attempt: Int16
}

struct EditJumpContext: Identifiable {
    let id = UUID()
    let jump: Jump
}

struct SessionDetailView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss

    @ObservedObject var session: Session
    @State private var isShowingEditSession = false
    @State private var isShowingExport = false
    @State private var isShowingVideoSharing = false
    @State private var athleteNotes: String
    @State private var coachNotes: String
    @State private var sessionTitle: String
    @State private var sessionDate: Date
    @State private var selectedAthlete: Athlete?

    // Navigation state for jump editing/creation using proper SwiftUI approach
    @State private var newJumpContext: NewJumpContext?
    @State private var editJumpContext: EditJumpContext?

    // Fetch jumps for this session with explicit entity description
    @FetchRequest private var jumps: FetchedResults<Jump>

    init(session: Session) {
        self.session = session
        _athleteNotes = State(initialValue: session.notesAthlete ?? "")
        _coachNotes = State(initialValue: session.notesCoach ?? "")
        _sessionTitle = State(initialValue: session.title ?? "")
        _sessionDate = State(initialValue: Date(timeIntervalSinceReferenceDate: session.date))
        _selectedAthlete = State(initialValue: session.athlete)

        // Configure the fetch request for jumps with explicit entity description
        _jumps = FetchRequest(fetchRequest: CoreDataFetchRequestHelper.createJumpFetchRequest(
            context: PersistenceController.shared.container.viewContext,
            sortDescriptors: [NSSortDescriptor(keyPath: \Jump.order, ascending: true)],
            predicate: NSPredicate(format: "session == %@", session)
        ))
    }

    var body: some View {
        VStack(spacing: 0) {
                // Grid view at the top
                JumpGridView(
                    session: session,
                    onNavigateToNewJump: { height, attempt in
                        newJumpContext = NewJumpContext(height: height, attempt: attempt)
                    },
                    onNavigateToEditJump: { jumpId in
                        // Find the jump in the current jumps collection
                        if let jump = jumps.first(where: { $0.id == jumpId }) {
                            editJumpContext = EditJumpContext(jump: jump)
                        }
                    }
                )
                .frame(height: 300) // Fixed height for the grid
                .id(session.id ?? UUID().uuidString) // Add an ID to preserve state

                // Jump details summary below
                JumpDetailsSummaryView(session: session)
                    .frame(maxHeight: .infinity) // Take remaining space
            }
        .navigationTitle(session.title ?? "Session")
        .navigationBarTitleDisplayMode(.inline)
        .sheet(isPresented: $isShowingEditSession) {
            SessionEditView(
                sessionTitle: $sessionTitle,
                sessionDate: $sessionDate,
                selectedAthlete: $selectedAthlete,
                athleteNotes: $athleteNotes,
                coachNotes: $coachNotes,
                onSave: saveSessionDetails
            )
        }
        .sheet(item: $newJumpContext) { context in
            NavigationStack {
                JumpViewNew(
                    session: session,
                    barHeightCm: context.height,
                    attemptIndex: context.attempt,
                    onSave: { jump in
                        // Clear navigation state
                        newJumpContext = nil
                    }
                )
            }
        }
        .sheet(item: $editJumpContext) { context in
            NavigationStack {
                JumpViewEdit(
                    jump: context.jump,
                    onSave: { updatedJump in
                        // Clear navigation state
                        editJumpContext = nil
                    }
                )
            }
        }
        .sheet(isPresented: $isShowingExport) {
            ExportSessionView(session: session)
        }
        .sheet(isPresented: $isShowingVideoSharing) {
            VideoSharingView(session: session)
        }
        .navigationBarItems(
            trailing: HStack {
                Button(action: {
                    finishSession()
                }) {
                    Text("Finish")
                        .bold()
                }

                Menu {
                    Button(action: {
                        isShowingEditSession = true
                    }) {
                        Label("Edit Session", systemImage: "pencil")
                    }

                    Button(action: {
                        isShowingExport = true
                    }) {
                        Label("Export", systemImage: "square.and.arrow.up")
                    }

                    // Share videos button (if any videos exist)
                    if getVideoCount() > 0 {
                        Button(action: {
                            isShowingVideoSharing = true
                        }) {
                            Label("Share Videos (\(getVideoCount()))", systemImage: "video")
                        }
                    }
                } label: {
                    Image(systemName: "ellipsis.circle")
                }
            }
        )
    }

    private func saveSessionDetails() {
        withAnimation {
            session.title = sessionTitle
            session.date = sessionDate.timeIntervalSinceReferenceDate
            session.athlete = selectedAthlete
            session.notesAthlete = athleteNotes
            session.notesCoach = coachNotes

            do {
                try viewContext.save()
            } catch {
                let nsError = error as NSError
                print("Error saving session details: \(nsError), \(nsError.userInfo)")
            }
        }
    }

    private func updateBestHeight() {
        // Find the best height among successful jumps
        let successfulJumps = jumps.filter { $0.result == "make" }
        let bestJump = successfulJumps.max(by: { $0.barHeightCm < $1.barHeightCm })

        if let bestJump = bestJump {
            session.bestHeightCm = bestJump.barHeightCm
            session.bestHeightIn = bestJump.barHeightIn
        } else {
            session.bestHeightCm = 0
            session.bestHeightIn = 0
        }

        do {
            try viewContext.save()
        } catch {
            let nsError = error as NSError
            print("Error updating best height: \(nsError), \(nsError.userInfo)")
        }
    }

    private func finishSession() {
        updateBestHeight()
        dismiss()
    }

    private func getVideoCount() -> Int {
        // Get all jumps for this session
        let jumps = session.jumps?.allObjects as? [Jump] ?? []

        // Collect all media items from all jumps
        var videoCount = 0
        for jump in jumps {
            let mediaItems = jump.getMediaItemsAsStructs()
            // Only count video media items
            let videoItems = mediaItems.filter { $0.type == .video }
            videoCount += videoItems.count
        }

        return videoCount
    }
}

// Session edit view as a separate component
struct SessionEditView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.managedObjectContext) private var viewContext
    @Binding var sessionTitle: String
    @Binding var sessionDate: Date
    @Binding var selectedAthlete: Athlete?
    @Binding var athleteNotes: String
    @Binding var coachNotes: String
    var onSave: () -> Void

    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Athlete.name, ascending: true)],
        animation: .default)
    private var athletes: FetchedResults<Athlete>

    var body: some View {
        NavigationStack {
            Form {
                Section(header: Text("Event Details")) {
                    TextField("Event Name", text: $sessionTitle)
                        .font(.headline)

                    DatePicker("Date", selection: $sessionDate, displayedComponents: [.date])
                }

                Section(header: Text("Athlete Assignment")) {
                    if athletes.isEmpty {
                        Text("No athletes found. Please create an athlete first.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    } else {
                        Picker("Athlete", selection: $selectedAthlete) {
                            Text("No athlete assigned").tag(nil as Athlete?)
                            ForEach(athletes, id: \.objectID) { athlete in
                                Text(athlete.name ?? "Unknown").tag(athlete as Athlete?)
                            }
                        }
                        .pickerStyle(MenuPickerStyle())

                        if let athlete = selectedAthlete {
                            HStack {
                                Text("Current Athlete:")
                                    .foregroundColor(.secondary)
                                Spacer()
                                Text(athlete.name ?? "Unknown")
                                    .fontWeight(.medium)
                            }
                        }
                    }
                }

                Section(header: Text("Athlete Notes")) {
                    TextEditor(text: $athleteNotes)
                        .frame(minHeight: 150)
                }

                Section(header: Text("Coach Notes")) {
                    TextEditor(text: $coachNotes)
                        .frame(minHeight: 150)
                }
            }
            .navigationTitle("Edit Session")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .confirmationAction) {
                    Button("Save") {
                        onSave()
                        dismiss()
                    }
                }
            }
        }
    }
}

struct SessionDetailPreview: View {
    var body: some View {
        let context = PersistenceController.preview.container.viewContext
        let session = Session.create(in: context)
        session.id = UUID().uuidString
        session.title = "Practice Session"
        session.type = "Practice"
        session.date = Date().timeIntervalSinceReferenceDate
        session.location = "School Track"
        session.weather = "Sunny, 75°F"

        return NavigationStack {
            SessionDetailView(session: session)
                .environment(\.managedObjectContext, context)
        }
    }
}

struct SessionEditPreview: View {
    @State private var sessionTitle = "Practice Session"
    @State private var sessionDate = Date()
    @State private var selectedAthlete: Athlete? = nil
    @State private var athleteNotes = "Sample athlete notes"
    @State private var coachNotes = "Sample coach notes"

    var body: some View {
        SessionEditView(
            sessionTitle: $sessionTitle,
            sessionDate: $sessionDate,
            selectedAthlete: $selectedAthlete,
            athleteNotes: $athleteNotes,
            coachNotes: $coachNotes,
            onSave: {}
        )
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
    }
}

#Preview("Session Detail") {
    SessionDetailPreview()
}

#Preview("Session Edit") {
    SessionEditPreview()
}
