import SwiftUI
import CloudKit
import CoreData

/// A view that provides diagnostic tools for CloudKit sync
struct CloudKitDiagnosticView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss

    // State for the diagnostic view
    @State private var isLoading = false
    @State private var diagnosticResults: [DiagnosticResult] = []
    @State private var errorMessage: String?
    @State private var showingErrorAlert = false
    @State private var currentProgress = 0
    @State private var totalProgress = 0
    @State private var entitySummaries: [EntitySummary] = []
    @State private var expandedEntityTypes: Set<String> = []

    // Alert state for force sync
    @State private var showAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""

    // State for schema inconsistency check


    // Verbose logging toggle
    @State private var verboseLogging = false

    // Available entity types from the CloudKitDiagnosticWrapper
    @State private var entityTypes: [String] = []

    var body: some View {
        NavigationStack {
            VStack {
                if isLoading {
                    VStack(spacing: 16) {
                        ProgressView(value: Double(currentProgress), total: Double(totalProgress))
                            .padding(.horizontal)

                        Text("Processing \(entityTypes[safe: currentProgress - 1] ?? "")... (\(currentProgress) of \(totalProgress))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                } else if !entitySummaries.isEmpty {
                    List {
                        ForEach(entitySummaries) { summary in
                            EntitySummaryRow(
                                summary: summary,
                                isExpanded: expandedEntityTypes.contains(summary.entityType),
                                onToggleExpand: { toggleEntityExpansion(summary.entityType) }
                            )
                        }
                    }
                } else {
                    ContentUnavailableView(
                        "No Results",
                        systemImage: "magnifyingglass",
                        description: Text("Run a diagnostic to compare local and iCloud data")
                    )
                    .padding()
                }

                // Action buttons
                VStack(spacing: 12) {
                    Button(action: runAllDiagnostics) {
                        Label("Run CloudKit Diagnostics", systemImage: "arrow.triangle.2.circlepath")
                            .frame(maxWidth: .infinity)
                            .padding()
                    }
                    .buttonStyle(.borderedProminent)
                    .disabled(isLoading)



                    // Verbose logging toggle
                    Toggle("Verbose Record Logging", isOn: $verboseLogging)
                        .onChange(of: verboseLogging) { oldValue, newValue in
                            CloudKitDiagnosticManager.verboseLogging = newValue
                        }
                        .padding(.top, 8)
                }
                .padding(.horizontal)
            }
            .navigationTitle("CloudKit Diagnostic")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarLeading) {
                    if !entitySummaries.isEmpty {
                        Button("Clear") {
                            clearResults()
                        }
                    }
                }
            }
            .alert("Error", isPresented: $showingErrorAlert) {
                Button("OK", role: .cancel) {}
            } message: {
                Text(errorMessage ?? "An unknown error occurred")
            }
            .alert(alertTitle, isPresented: $showAlert) {
                Button("OK", role: .cancel) {}
            } message: {
                Text(alertMessage)
            }

            .onAppear {
                // Initialize entity types
                entityTypes = CloudKitDiagnosticWrapper.shared.getEntityTypes()

                // Initialize verbose logging state
                verboseLogging = CloudKitDiagnosticManager.verboseLogging
            }
        }
    }

    /// Toggles the expansion state of an entity type
    private func toggleEntityExpansion(_ entityType: String) {
        if expandedEntityTypes.contains(entityType) {
            expandedEntityTypes.remove(entityType)
        } else {
            expandedEntityTypes.insert(entityType)
        }
    }

    // The forceSyncRecord method has been removed to follow CloudKit best practices
    // Let NSPersistentCloudKitContainer handle the sync process automatically

    /// Clears the diagnostic results
    private func clearResults() {
        entitySummaries = []
        diagnosticResults = []
        expandedEntityTypes = []
    }

    /// Runs diagnostics for all entity types
    private func runAllDiagnostics() {
        isLoading = true
        diagnosticResults = []
        entitySummaries = []
        expandedEntityTypes = []

        // Set up progress tracking
        let allEntityTypes = CloudKitDiagnosticWrapper.shared.getEntityTypes()
        entityTypes = allEntityTypes
        totalProgress = allEntityTypes.count
        currentProgress = 0

        // Process each entity type sequentially
        processNextEntityType(allEntityTypes: allEntityTypes, index: 0, summaries: [])
    }

    /// Processes the next entity type in the sequence
    private func processNextEntityType(allEntityTypes: [String], index: Int, summaries: [EntitySummary]) {
        // Check if we've processed all entity types
        if index >= allEntityTypes.count {
            DispatchQueue.main.async {
                self.isLoading = false
                self.entitySummaries = summaries.sorted(by: { $0.entityType < $1.entityType })
            }
            return
        }

        // Update progress
        let entityType = allEntityTypes[index]
        DispatchQueue.main.async {
            self.currentProgress = index + 1
        }

        // Process the current entity type using the diagnostic wrapper
        CloudKitDiagnosticWrapper.shared.runDiagnostics(
            forEntityType: entityType,
            in: viewContext
        ) { result, error in
            var newSummaries = summaries

            if let error = error {
                print("Error processing \(entityType): \(error.localizedDescription)")
                // Add an error summary and continue
                let errorSummary = EntitySummary(
                    entityType: entityType,
                    localCount: 0,
                    cloudCount: 0,
                    matchingCount: 0,
                    localOnlyCount: 0,
                    cloudOnlyCount: 0,
                    matchingRecords: [],
                    hasError: true,
                    errorMessage: error.localizedDescription
                )
                newSummaries.append(errorSummary)
            } else if let result = result {
                // Create a summary for this entity type
                let matchingRecords = self.createMatchingRecords(from: result, entityType: entityType)

                let summary = EntitySummary(
                    entityType: entityType,
                    localCount: result.localRecordCount,
                    cloudCount: result.cloudRecordCount,
                    matchingCount: result.syncedRecords.count,
                    localOnlyCount: result.localOnlyRecords.count,
                    cloudOnlyCount: result.cloudOnlyRecords.count,
                    matchingRecords: matchingRecords,
                    hasError: false,
                    errorMessage: nil
                )

                newSummaries.append(summary)

                // Also store the detailed diagnostic results
                DispatchQueue.main.async {
                    self.diagnosticResults.append(contentsOf: self.createDiagnosticResults(from: result, entityType: entityType))
                }
            }

            // Process the next entity type
            self.processNextEntityType(allEntityTypes: allEntityTypes, index: index + 1, summaries: newSummaries)
        }
    }

    /// Creates matching record information from a comparison result
    private func createMatchingRecords(from result: ComparisonResult, entityType: String) -> [MatchingRecord] {
        var matchingRecords: [MatchingRecord] = []

        // Helper function to check if an entity has an "id" property
        func entityHasIdProperty(_ record: NSManagedObject) -> Bool {
            let entity = record.entity
            return entity.attributesByName["id"] != nil
        }

        // Check if the entity type has an "id" property
        let hasIdProperty = !result.localRecords.isEmpty && entityHasIdProperty(result.localRecords[0])

        // Process synced records
        for (localRecord, cloudRecord) in result.syncedRecords {
            // Get ID from local record, or use record name as fallback
            let id: String
            let cloudId: String
            var displayName: String = ""

            if hasIdProperty {
                if let recordId = localRecord.value(forKey: "id") as? String, !recordId.isEmpty {
                    id = recordId
                } else {
                    // Use cloud record ID as fallback
                    id = "record_\(cloudRecord.recordID.recordName)"
                }

                if let cloudRecordId = cloudRecord["id"] as? String, !cloudRecordId.isEmpty {
                    cloudId = cloudRecordId
                } else {
                    cloudId = cloudRecord.recordID.recordName
                }

                // Try to get a human-readable name based on entity type
                if entityType == "CD_Athlete" {
                    displayName = localRecord.value(forKey: "name") as? String ?? ""
                } else if entityType == "CD_Session" {
                    let title = localRecord.value(forKey: "title") as? String ?? ""
                    let type = localRecord.value(forKey: "type") as? String ?? ""
                    let date = localRecord.value(forKey: "date") as? TimeInterval ?? 0
                    let dateStr = Date(timeIntervalSinceReferenceDate: date).formatted(date: .abbreviated, time: .omitted)
                    displayName = "\(title) (\(type)) - \(dateStr)"
                } else if entityType == "CD_Jump" {
                    let heightCm = localRecord.value(forKey: "barHeightCm") as? Double ?? 0
                    let result = localRecord.value(forKey: "result") as? String ?? ""
                    displayName = "\(Int(heightCm))cm - \(result.capitalized)"
                } else if entityType == "CD_Pole" {
                    let name = localRecord.value(forKey: "name") as? String ?? ""
                    let brand = localRecord.value(forKey: "brand") as? String ?? ""
                    displayName = "\(name) (\(brand))"
                }
            } else {
                // For entities without an "id" property, use the record name
                id = localRecord.objectID.uriRepresentation().lastPathComponent
                cloudId = cloudRecord.recordID.recordName
            }

            let matchingRecord = MatchingRecord(
                localId: id,
                cloudId: cloudId,
                recordName: cloudRecord.recordID.recordName,
                displayName: displayName
            )

            matchingRecords.append(matchingRecord)
        }

        return matchingRecords
    }

    /// Creates diagnostic results from a comparison result
    private func createDiagnosticResults(from result: ComparisonResult, entityType: String) -> [DiagnosticResult] {
        var diagnosticResults: [DiagnosticResult] = []
        var localOnlyRecords: [RecordSummary] = []
        var cloudOnlyRecords: [RecordSummary] = []

        // Helper function to check if an entity has an "id" property
        func entityHasIdProperty(_ record: NSManagedObject) -> Bool {
            let entity = record.entity
            return entity.attributesByName["id"] != nil
        }

        // Helper function to get a display name for a local record
        func getDisplayNameForLocalRecord(_ record: NSManagedObject) -> String {
            // Start with a fallback display name based on the entity type and object ID
            let objectIdString = record.objectID.uriRepresentation().lastPathComponent
            let fallbackName = "\(entityType) (\(objectIdString.prefix(8)))"

            // Try to get a more descriptive name based on entity type
            if entityType == "CD_Athlete" {
                let name = record.value(forKey: "name") as? String ?? ""
                return name.isEmpty ? fallbackName : name
            } else if entityType == "CD_Session" {
                let title = record.value(forKey: "title") as? String ?? ""
                let type = record.value(forKey: "type") as? String ?? ""

                // Handle date with more robust type checking
                var dateStr = ""
                if let dateTimeInterval = record.value(forKey: "date") as? TimeInterval {
                    dateStr = Date(timeIntervalSinceReferenceDate: dateTimeInterval).formatted(date: .abbreviated, time: .omitted)
                } else if let dateValue = record.value(forKey: "date") as? Date {
                    dateStr = dateValue.formatted(date: .abbreviated, time: .omitted)
                } else if let dateNumber = record.value(forKey: "date") as? NSNumber {
                    dateStr = Date(timeIntervalSinceReferenceDate: dateNumber.doubleValue).formatted(date: .abbreviated, time: .omitted)
                }

                let displayName = "\(title) (\(type)) - \(dateStr)"
                return displayName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? fallbackName : displayName
            } else if entityType == "CD_Jump" {
                // Handle height with more robust type checking
                var heightStr = "0"
                if let heightCm = record.value(forKey: "barHeightCm") as? Double {
                    heightStr = "\(Int(heightCm))"
                } else if let heightCm = record.value(forKey: "barHeightCm") as? NSNumber {
                    heightStr = "\(Int(heightCm.doubleValue))"
                } else if let heightCm = record.value(forKey: "barHeightCm") as? Int {
                    heightStr = "\(heightCm)"
                } else if let heightCm = record.value(forKey: "barHeightCm") as? String, let height = Double(heightCm) {
                    heightStr = "\(Int(height))"
                }

                let result = record.value(forKey: "result") as? String ?? ""
                let displayName = "\(heightStr)cm - \(result.capitalized)"
                return displayName == "0cm - " ? fallbackName : displayName
            } else if entityType == "CD_Pole" {
                let name = record.value(forKey: "name") as? String ?? ""
                let brand = record.value(forKey: "brand") as? String ?? ""
                let displayName = "\(name) (\(brand))"
                return displayName == " ()" ? fallbackName : displayName
            }

            // If we couldn't generate a meaningful name, return the fallback
            return fallbackName
        }

        // Helper function to get a display name for a cloud record
        func getDisplayNameForCloudRecord(_ record: CKRecord) -> String {
            // Start with a fallback display name based on the record ID
            let fallbackName = "\(entityType) (\(record.recordID.recordName.prefix(8)))"

            // Try to get a more descriptive name based on entity type
            if entityType == "CD_Athlete" {
                let name = record["name"] as? String ?? ""
                return name.isEmpty ? fallbackName : name
            } else if entityType == "CD_Session" {
                let title = record["title"] as? String ?? ""
                let type = record["type"] as? String ?? ""

                // Handle date with more robust type checking
                var dateStr = ""
                if let date = record["date"] as? Date {
                    dateStr = date.formatted(date: .abbreviated, time: .omitted)
                } else if let dateTimeInterval = record["date"] as? TimeInterval {
                    dateStr = Date(timeIntervalSinceReferenceDate: dateTimeInterval).formatted(date: .abbreviated, time: .omitted)
                } else if let dateNumber = record["date"] as? NSNumber {
                    dateStr = Date(timeIntervalSinceReferenceDate: dateNumber.doubleValue).formatted(date: .abbreviated, time: .omitted)
                }

                let displayName = "\(title) (\(type)) - \(dateStr)"
                return displayName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? fallbackName : displayName
            } else if entityType == "CD_Jump" {
                // Handle height with more robust type checking
                var heightStr = "0"
                if let heightCm = record["barHeightCm"] as? Double {
                    heightStr = "\(Int(heightCm))"
                } else if let heightCm = record["barHeightCm"] as? NSNumber {
                    heightStr = "\(Int(heightCm.doubleValue))"
                } else if let heightCm = record["barHeightCm"] as? Int {
                    heightStr = "\(heightCm)"
                } else if let heightCm = record["barHeightCm"] as? String, let height = Double(heightCm) {
                    heightStr = "\(Int(height))"
                }

                let result = record["result"] as? String ?? ""
                let displayName = "\(heightStr)cm - \(result.capitalized)"
                return displayName == "0cm - " ? fallbackName : displayName
            } else if entityType == "CD_Pole" {
                let name = record["name"] as? String ?? ""
                let brand = record["brand"] as? String ?? ""
                let displayName = "\(name) (\(brand))"
                return displayName == " ()" ? fallbackName : displayName
            }

            // If we couldn't generate a meaningful name, return the fallback
            return fallbackName
        }

        // Check if the entity type has an "id" property
        let hasIdProperty = !result.localRecords.isEmpty && entityHasIdProperty(result.localRecords[0])

        // Process synced records
        for (localRecord, cloudRecord) in result.syncedRecords {
            // Get ID from local record, or use record name as fallback
            let id: String
            if hasIdProperty {
                if let recordId = localRecord.value(forKey: "id") as? String, !recordId.isEmpty {
                    id = recordId
                } else {
                    // Use cloud record ID as fallback
                    id = "record_\(cloudRecord.recordID.recordName)"
                }
            } else {
                // For entities without an "id" property, use the record name
                id = cloudRecord.recordID.recordName
            }

            let diagnosticResult = DiagnosticResult(
                id: id,
                entityType: entityType,
                existsLocally: true,
                existsInCloud: true,
                localData: getLocalRecordDetails(localRecord),
                cloudData: getCloudRecordDetails(cloudRecord)
            )

            diagnosticResults.append(diagnosticResult)
        }

        // Process local-only records
        for localRecord in result.localOnlyRecords {
            // Get ID from local record or generate a unique one
            let id: String
            if hasIdProperty {
                if let recordId = localRecord.value(forKey: "id") as? String, !recordId.isEmpty {
                    id = recordId
                } else {
                    // Generate a unique ID for records without an ID
                    id = "local_\(UUID().uuidString.prefix(8))"
                }
            } else {
                // For entities without an "id" property, use the objectID
                id = "local_\(localRecord.objectID.uriRepresentation().lastPathComponent)"
            }

            let localDetails = getLocalRecordDetails(localRecord)
            let displayName = getDisplayNameForLocalRecord(localRecord)

            // Create a record summary for the UI
            let recordSummary = RecordSummary(
                recordId: id,
                recordName: localRecord.objectID.uriRepresentation().lastPathComponent,
                displayName: displayName,
                details: localDetails
            )
            localOnlyRecords.append(recordSummary)

            let diagnosticResult = DiagnosticResult(
                id: id,
                entityType: entityType,
                existsLocally: true,
                existsInCloud: false,
                localData: localDetails,
                cloudData: nil
            )

            diagnosticResults.append(diagnosticResult)
        }

        // Process cloud-only records
        for cloudRecord in result.cloudOnlyRecords {
            // Get ID from cloud record or use record name
            let id: String
            if hasIdProperty {
                if let recordId = cloudRecord["id"] as? String, !recordId.isEmpty {
                    id = recordId
                } else {
                    // Use record name as ID for records without an ID field
                    id = "cloud_\(cloudRecord.recordID.recordName)"
                }
            } else {
                // For entities without an "id" property, use the record name
                id = "cloud_\(cloudRecord.recordID.recordName)"
            }

            let cloudDetails = getCloudRecordDetails(cloudRecord)
            let displayName = getDisplayNameForCloudRecord(cloudRecord)

            // Create a record summary for the UI
            let recordSummary = RecordSummary(
                recordId: id,
                recordName: cloudRecord.recordID.recordName,
                displayName: displayName,
                details: cloudDetails
            )
            cloudOnlyRecords.append(recordSummary)

            let diagnosticResult = DiagnosticResult(
                id: id,
                entityType: entityType,
                existsLocally: false,
                existsInCloud: true,
                localData: nil,
                cloudData: cloudDetails
            )

            diagnosticResults.append(diagnosticResult)
        }

        // Store the local-only and cloud-only records in the entity summary
        DispatchQueue.main.async {
            if let index = self.entitySummaries.firstIndex(where: { $0.entityType == entityType }) {
                self.entitySummaries[index].localOnlyRecords = localOnlyRecords
                self.entitySummaries[index].cloudOnlyRecords = cloudOnlyRecords
            }
        }

        return diagnosticResults
    }

    /// Extracts details from a local Core Data record
    private func getLocalRecordDetails(_ record: NSManagedObject) -> [String: String] {
        var details: [String: String] = [:]

        // Add the object ID as a special field for debugging
        let objectIdString = record.objectID.uriRepresentation().lastPathComponent
        details["_objectID"] = objectIdString

        // Add entity name
        details["_entityName"] = record.entity.name ?? "Unknown"

        // Check if the record has an ID field
        let hasIdField = record.entity.attributesByName.keys.contains("id")
        let idValue = hasIdField ? (record.value(forKey: "id") as? String ?? "<nil>") : "<missing id field>"
        details["_hasProperID"] = hasIdField ? "Yes" : "No"
        details["_idValue"] = idValue

        // Process all attributes
        for attribute in record.entity.attributesByName {
            if let value = record.value(forKey: attribute.key) {
                // Format dates in a more readable way
                if let date = value as? Date {
                    let formatter = DateFormatter()
                    formatter.dateStyle = .medium
                    formatter.timeStyle = .short
                    details[attribute.key] = formatter.string(from: date)
                }
                // Format TimeInterval as a date if it's likely a date
                else if let timeInterval = value as? TimeInterval, timeInterval > 0 {
                    let date = Date(timeIntervalSinceReferenceDate: timeInterval)
                    let formatter = DateFormatter()
                    formatter.dateStyle = .medium
                    formatter.timeStyle = .short
                    details[attribute.key] = formatter.string(from: date)
                }
                // Handle binary data specially
                else if let data = value as? Data {
                    details[attribute.key] = "<Binary Data: \(data.count) bytes>"
                }
                // Default string representation
                else {
                    details[attribute.key] = String(describing: value)
                }
            } else {
                details[attribute.key] = "<nil>"
            }
        }

        // Add relationship information
        for relationship in record.entity.relationshipsByName {
            let name = relationship.key
            let isToMany = relationship.value.isToMany

            if isToMany {
                if let relatedObjects = record.value(forKey: name) as? NSSet {
                    details[name] = "<Relationship: \(relatedObjects.count) objects>"
                } else {
                    details[name] = "<Empty Relationship>"
                }
            } else {
                if let relatedObject = record.value(forKey: name) as? NSManagedObject {
                    // Try to get the ID, but don't crash if the related object doesn't have an ID field
                    let relatedId: String
                    if relatedObject.entity.attributesByName.keys.contains("id") {
                        relatedId = relatedObject.value(forKey: "id") as? String ?? "unknown"
                    } else {
                        relatedId = relatedObject.objectID.uriRepresentation().lastPathComponent
                    }
                    details[name] = "<Related: \(relatedId)>"
                } else {
                    details[name] = "<No Related Object>"
                }
            }
        }

        return details
    }

    /// Extracts details from a CloudKit record
    private func getCloudRecordDetails(_ record: CKRecord) -> [String: String] {
        var details: [String: String] = [:]

        // Add record ID and type with special prefixes for easy identification
        details["_recordID"] = record.recordID.recordName
        details["_recordType"] = record.recordType
        details["_zoneID"] = record.recordID.zoneID.zoneName

        // Add creation and modification dates
        if let creationDate = record.creationDate {
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .short
            details["_creationDate"] = formatter.string(from: creationDate)
        }

        if let modificationDate = record.modificationDate {
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .short
            details["_modificationDate"] = formatter.string(from: modificationDate)
        }

        // Process each key in the record with better formatting
        for key in record.allKeys() {
            if let value = record[key] {
                // Make sure we don't have empty keys
                let safeKey = key.isEmpty ? "_empty_" : key

                // Format dates in a more readable way
                if let date = value as? Date {
                    let formatter = DateFormatter()
                    formatter.dateStyle = .medium
                    formatter.timeStyle = .short
                    details[safeKey] = formatter.string(from: date)
                }
                // Handle CKAsset specially
                else if let asset = value as? CKAsset {
                    if let fileURL = asset.fileURL {
                        details[safeKey] = "<Asset: \(fileURL.lastPathComponent)>"
                    } else {
                        details[safeKey] = "<Asset: no file URL>"
                    }
                }
                // Handle CKReference specially
                else if let reference = value as? CKRecord.Reference {
                    details[safeKey] = "<Reference: \(reference.recordID.recordName)>"
                }
                // Handle binary data specially
                else if let data = value as? Data {
                    details[safeKey] = "<Binary Data: \(data.count) bytes>"
                }
                // Default string representation
                else {
                    details[safeKey] = String(describing: value)
                }
            }
        }

        return details
    }


}

/// Represents a matching record between local and cloud
struct MatchingRecord: Identifiable {
    let id = UUID()
    let localId: String
    let cloudId: String
    let recordName: String
    var displayName: String = ""  // Human-readable name for the record
}

/// Represents a summary of a single record (either local or cloud)
struct RecordSummary: Identifiable {
    let id = UUID()
    let recordId: String          // The record's ID
    let recordName: String        // The CloudKit record name or local object ID
    let displayName: String       // Human-readable name for the record
    let details: [String: String] // Additional details about the record
}

/// Represents a summary of an entity type's sync status
struct EntitySummary: Identifiable {
    let id = UUID()
    let entityType: String
    let localCount: Int
    let cloudCount: Int
    let matchingCount: Int
    let localOnlyCount: Int
    let cloudOnlyCount: Int
    let matchingRecords: [MatchingRecord]
    let hasError: Bool
    let errorMessage: String?

    // Local-only and cloud-only records
    var localOnlyRecords: [RecordSummary] = []
    var cloudOnlyRecords: [RecordSummary] = []

    var statusDescription: String {
        if hasError {
            return "Error"
        } else if localOnlyCount > 0 || cloudOnlyCount > 0 {
            var parts: [String] = []
            if matchingCount > 0 {
                parts.append("\(matchingCount) synced")
            }
            if localOnlyCount > 0 {
                parts.append("\(localOnlyCount) local-only")
            }
            if cloudOnlyCount > 0 {
                parts.append("\(cloudOnlyCount) cloud-only")
            }
            return parts.joined(separator: ", ")
        } else if matchingCount > 0 {
            return "All \(matchingCount) records synced"
        } else {
            return "No records"
        }
    }

    var statusColor: Color {
        if hasError {
            return .red
        } else if localOnlyCount > 0 || cloudOnlyCount > 0 {
            return .orange
        } else if matchingCount > 0 {
            return .green
        } else {
            return .gray
        }
    }

    var hasMismatches: Bool {
        return localOnlyCount > 0 || cloudOnlyCount > 0
    }
}

/// A row displaying an entity summary
struct EntitySummaryRow: View {
    let summary: EntitySummary
    let isExpanded: Bool
    let onToggleExpand: () -> Void

    // Add state for debug info alerts
    @State private var showAlert = false
    @State private var alertTitle = ""
    @State private var alertMessage = ""

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            // Header row
            Button(action: onToggleExpand) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(summary.entityType)
                            .font(.headline)

                        HStack(spacing: 12) {
                            Label("\(summary.localCount) local", systemImage: "iphone")
                                .font(.caption)

                            Label("\(summary.cloudCount) cloud", systemImage: "icloud")
                                .font(.caption)
                        }
                        .foregroundColor(.secondary)
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: 4) {
                        Text(summary.statusDescription)
                            .font(.subheadline)
                            .foregroundColor(summary.statusColor)

                        if summary.hasError {
                            Text(summary.errorMessage ?? "Unknown error")
                                .font(.caption)
                                .foregroundColor(.red)
                                .lineLimit(1)
                        }
                    }

                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .foregroundColor(.gray)
                        .padding(.leading, 8)
                }
                .contentShape(Rectangle())
            }
            .buttonStyle(.plain)

            // Expanded content
            if isExpanded && !summary.hasError {
                // Matching Records Section
                if summary.matchingCount > 0 {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Synced Records:")
                            .font(.subheadline)
                            .bold()
                            .padding(.top, 4)

                        ForEach(summary.matchingRecords) { record in
                            HStack(alignment: .top) {
                                VStack(alignment: .leading, spacing: 4) {
                                    // Always show a display name
                                    Text(record.displayName.isEmpty ? "Synced Record" : record.displayName)
                                        .font(.caption)
                                        .bold()

                                    // Show ID with a shorter format
                                    Text("ID: \(record.localId.prefix(12))...")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }

                                Spacer()

                                Text("✓")
                                    .foregroundColor(.green)
                                    .font(.caption)
                                    .bold()
                            }
                            .padding(.vertical, 4)
                            .padding(.horizontal, 8)
                            .background(Color.green.opacity(0.1))
                            .cornerRadius(8)
                        }
                    }
                    .padding(.leading)
                }

                // Local-only Records Section
                if summary.localOnlyCount > 0 {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Local-only Records:")
                            .font(.subheadline)
                            .bold()
                            .foregroundColor(.orange)
                            .padding(.top, 4)

                        ForEach(summary.localOnlyRecords) { record in
                            HStack(alignment: .top) {
                                VStack(alignment: .leading, spacing: 4) {
                                    // Always show a display name (our improved functions ensure there's always something)
                                    Text(record.displayName)
                                        .font(.caption)
                                        .bold()

                                    // Show ID with a shorter format and highlight if it's an objectID
                                    if record.recordId.hasPrefix("local_") {
                                        Text("ID: [objectID] \(record.recordId.dropFirst(6).prefix(12))...")
                                            .font(.caption)
                                            .foregroundColor(.orange)
                                    } else {
                                        Text("ID: \(record.recordId.prefix(12))...")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }

                                    // Show key details based on entity type
                                    if summary.entityType == "CD_Jump" {
                                        if let height = record.details["barHeightCm"] {
                                            Text("Height: \(height)")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                        if let result = record.details["result"] {
                                            Text("Result: \(result)")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                    } else if summary.entityType == "CD_Session" {
                                        if let date = record.details["date"] {
                                            Text("Date: \(date)")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                        if let title = record.details["title"] {
                                            Text("Title: \(title)")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                    } else if summary.entityType == "CD_Pole" {
                                        if let name = record.details["name"] {
                                            Text("Name: \(name)")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                        if let brand = record.details["brand"] {
                                            Text("Brand: \(brand)")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                    }

                                    // Add a "Show Debug Info" button
                                    Button(action: {
                                        // Create a debug message with all record details
                                        var debugInfo = ""

                                        // Add special section for ID information
                                        let hasProperID = record.details["_hasProperID"] == "Yes"
                                        let idValue = record.details["_idValue"] ?? "<unknown>"
                                        let objectID = record.details["_objectID"] ?? "<unknown>"

                                        debugInfo += "ID INFORMATION:\n"
                                        debugInfo += "• Has ID field: \(hasProperID ? "Yes" : "No")\n"
                                        debugInfo += "• ID value: \(idValue)\n"
                                        debugInfo += "• Object ID: \(objectID)\n"
                                        debugInfo += "• Record ID used: \(record.recordId)\n\n"

                                        // Add warning for missing ID
                                        if !hasProperID || idValue == "<nil>" {
                                            debugInfo += "⚠️ WARNING: This record is missing a proper ID field or value.\n"
                                            debugInfo += "This may prevent proper CloudKit synchronization.\n\n"
                                        }

                                        debugInfo += "RECORD DETAILS:\n"
                                        for (key, value) in record.details.sorted(by: { $0.key < $1.key }) {
                                            // Skip the special fields we already displayed
                                            if !key.hasPrefix("_") {
                                                debugInfo += "• \(key): \(value)\n"
                                            }
                                        }

                                        // Show the debug info in an alert
                                        alertTitle = "Debug Info: \(record.displayName)"
                                        alertMessage = debugInfo
                                        showAlert = true
                                    }) {
                                        Text(record.recordId.hasPrefix("local_") ? "Debug Info ⚠️" : "Debug Info")
                                            .font(.caption)
                                            .foregroundColor(record.recordId.hasPrefix("local_") ? .orange : .blue)
                                    }
                                    .buttonStyle(BorderlessButtonStyle())
                                }

                                Spacer()

                                // Info label about CloudKit sync
                                Text("Waiting for CloudKit sync")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                            }
                            .padding(.vertical, 4)
                            .padding(.horizontal, 8)
                            .background(Color.orange.opacity(0.1))
                            .cornerRadius(8)
                        }
                    }
                    .padding(.leading)
                }

                // Cloud-only Records Section
                if summary.cloudOnlyCount > 0 {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Cloud-only Records:")
                            .font(.subheadline)
                            .bold()
                            .foregroundColor(.blue)
                            .padding(.top, 4)

                        ForEach(summary.cloudOnlyRecords) { record in
                            HStack(alignment: .top) {
                                VStack(alignment: .leading, spacing: 4) {
                                    // Always show a display name (our improved functions ensure there's always something)
                                    Text(record.displayName)
                                        .font(.caption)
                                        .bold()

                                    // Show ID with a shorter format and highlight if it's a cloud-only ID
                                    if record.recordId.hasPrefix("cloud_") {
                                        Text("ID: [recordName] \(record.recordId.dropFirst(6).prefix(12))...")
                                            .font(.caption)
                                            .foregroundColor(.blue)
                                    } else {
                                        Text("ID: \(record.recordId.prefix(12))...")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }

                                    // Show key details based on entity type
                                    if summary.entityType == "CD_Jump" {
                                        if let height = record.details["barHeightCm"] {
                                            Text("Height: \(height)")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                        if let result = record.details["result"] {
                                            Text("Result: \(result)")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                    } else if summary.entityType == "CD_Session" {
                                        if let date = record.details["date"] {
                                            Text("Date: \(date)")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                        if let title = record.details["title"] {
                                            Text("Title: \(title)")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                    } else if summary.entityType == "CD_Pole" {
                                        if let name = record.details["name"] {
                                            Text("Name: \(name)")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                        if let brand = record.details["brand"] {
                                            Text("Brand: \(brand)")
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }
                                    }

                                    // Add a "Show Debug Info" button
                                    Button(action: {
                                        // Create a debug message with all record details
                                        var debugInfo = ""

                                        // Add special section for ID information
                                        let recordID = record.details["_recordID"] ?? "<unknown>"
                                        let recordType = record.details["_recordType"] ?? "<unknown>"
                                        let zoneID = record.details["_zoneID"] ?? "<unknown>"

                                        debugInfo += "CLOUD RECORD INFORMATION:\n"
                                        debugInfo += "• Record ID: \(recordID)\n"
                                        debugInfo += "• Record Type: \(recordType)\n"
                                        debugInfo += "• Zone ID: \(zoneID)\n"
                                        debugInfo += "• Record ID used: \(record.recordId)\n\n"

                                        // Check for ID fields
                                        let hasRegularId = record.details["id"] != nil
                                        let hasPrefixedId = record.details["CD_id"] != nil
                                        let regularIdValue = record.details["id"] ?? "<nil>"
                                        let prefixedIdValue = record.details["CD_id"] ?? "<nil>"

                                        debugInfo += "ID FIELDS:\n"
                                        debugInfo += "• Has 'id' field: \(hasRegularId ? "Yes" : "No")\n"
                                        debugInfo += "• 'id' value: \(regularIdValue)\n"
                                        debugInfo += "• Has 'CD_id' field: \(hasPrefixedId ? "Yes" : "No")\n"
                                        debugInfo += "• 'CD_id' value: \(prefixedIdValue)\n\n"

                                        // Add warning for missing ID
                                        if !hasRegularId && !hasPrefixedId {
                                            debugInfo += "⚠️ WARNING: This cloud record is missing both 'id' and 'CD_id' fields.\n"
                                            debugInfo += "This may prevent proper CloudKit synchronization.\n\n"
                                        }

                                        debugInfo += "RECORD DETAILS:\n"
                                        for (key, value) in record.details.sorted(by: { $0.key < $1.key }) {
                                            // Skip the special fields we already displayed
                                            if !key.hasPrefix("_") && key != "id" && key != "CD_id" {
                                                debugInfo += "• \(key): \(value)\n"
                                            }
                                        }

                                        // Show the debug info in an alert
                                        alertTitle = "Debug Info: \(record.displayName)"
                                        alertMessage = debugInfo
                                        showAlert = true
                                    }) {
                                        Text(record.recordId.hasPrefix("cloud_") ? "Debug Info ⚠️" : "Debug Info")
                                            .font(.caption)
                                            .foregroundColor(record.recordId.hasPrefix("cloud_") ? .blue : .blue)
                                    }
                                    .buttonStyle(BorderlessButtonStyle())
                                }

                                Spacer()
                            }
                            .padding(.vertical, 4)
                            .padding(.horizontal, 8)
                            .background(Color.blue.opacity(0.1))
                            .cornerRadius(8)
                        }
                    }
                    .padding(.leading)
                }
            }
        }
        .padding(.vertical, 8)
        .alert(isPresented: $showAlert) {
            Alert(
                title: Text(alertTitle),
                message: Text(alertMessage),
                dismissButton: .default(Text("OK"))
            )
        }
    }
}

/// Represents a diagnostic result comparing local and cloud data
struct DiagnosticResult: Identifiable {
    let id: String
    let entityType: String
    let existsLocally: Bool
    let existsInCloud: Bool
    let localData: [String: String]?
    let cloudData: [String: String]?

    var syncStatus: SyncStatus {
        if existsLocally && existsInCloud {
            return .synced
        } else if existsLocally && !existsInCloud {
            return .localOnly
        } else if !existsLocally && existsInCloud {
            return .cloudOnly
        } else {
            return .unknown
        }
    }

    enum SyncStatus {
        case synced
        case localOnly
        case cloudOnly
        case unknown

        var description: String {
            switch self {
            case .synced: return "Synced"
            case .localOnly: return "Local Only"
            case .cloudOnly: return "Cloud Only"
            case .unknown: return "Unknown"
            }
        }

        var color: Color {
            switch self {
            case .synced: return .green
            case .localOnly: return .orange
            case .cloudOnly: return .blue
            case .unknown: return .gray
            }
        }
    }
}

// Extension to get unique elements from an array
extension Array where Element: Hashable {
    func uniqued() -> [Element] {
        var seen = Set<Element>()
        return filter { seen.insert($0).inserted }
    }
}

// Extension to safely access array elements
extension Array {
    subscript(safe index: Int) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}

#Preview {
    CloudKitDiagnosticView()
        .environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
