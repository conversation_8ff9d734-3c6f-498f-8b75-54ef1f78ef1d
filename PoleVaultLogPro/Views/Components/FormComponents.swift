import SwiftUI
import CoreData

// MARK: - Form Section Components

/// A reusable result picker section for jump forms
struct ResultSection: View {
    @Binding var result: String
    let shouldDisablePass: Bool

    var body: some View {
        Section(header: Text("Result")) {
            Picker("Result", selection: $result) {
                Text("Make").tag("make")
                Text("Miss").tag("miss")
                if !shouldDisablePass {
                    Text("Pass").tag("pass")
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
}

/// A reusable height and attempt display section (read-only)
struct HeightAttemptSection: View {
    let barHeightCm: Double
    let attemptIndex: Int16

    var body: some View {
        Section(header: Text("Height & Attempt")) {
            HStack {
                Text("Height")
                Spacer()
                let heightFormat = HeightConverter.formatHeightTwoLine(cm: barHeightCm)
                VStack(alignment: .trailing) {
                    Text(heightFormat.primary)
                        .fontWeight(.bold)
                    if let secondary = heightFormat.secondary {
                        Text(secondary)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            HStack {
                Text("Attempt")
                Spacer()
                Text("#\(attemptIndex)")
                    .foregroundColor(.secondary)
            }
        }
    }
}

/// A reusable height and attempt section with dropdown pickers that prevent conflicts
struct HeightAttemptEditSection: View {
    @Binding var barHeightCm: Double
    @Binding var attemptIndex: Int16
    let session: Session
    let currentJump: Jump?

    @State private var showingHeightPicker = false
    @State private var showingAttemptPicker = false

    var body: some View {
        Section(header: Text("Height & Attempt")) {
            // Height selection
            NavigationLink(destination: ConflictAwareHeightPickerView(
                selectedHeight: $barHeightCm,
                selectedAttempt: attemptIndex,
                session: session,
                currentJump: currentJump
            )) {
                HStack {
                    Text("Height")
                    Spacer()
                    let heightFormat = HeightConverter.formatHeightTwoLine(cm: barHeightCm)
                    VStack(alignment: .trailing) {
                        Text(heightFormat.primary)
                            .fontWeight(.bold)
                        if let secondary = heightFormat.secondary {
                            Text(secondary)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }
            }
            .foregroundColor(.primary)

            // Attempt selection
            NavigationLink(destination: ConflictAwareAttemptPickerView(
                selectedHeight: barHeightCm,
                selectedAttempt: $attemptIndex,
                session: session,
                currentJump: currentJump
            )) {
                HStack {
                    Text("Attempt")
                    Spacer()
                    Text("#\(attemptIndex)")
                        .foregroundColor(.primary)
                }
            }
            .foregroundColor(.primary)
        }
    }
}

/// A height picker that shows only the heights available in the current session
struct SessionHeightPickerView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var selectedHeight: Double
    let session: Session
    let onSelect: (Double) -> Void

    private var availableHeights: [Double] {
        session.getGridHeights().sorted(by: >)
    }

    var body: some View {
        List {
            ForEach(availableHeights, id: \.self) { height in
                Button(action: {
                    selectedHeight = height
                    onSelect(height)
                    dismiss()
                }) {
                    HStack {
                        let heightFormat = HeightConverter.formatHeightTwoLine(cm: height)
                        VStack(alignment: .leading) {
                            Text(heightFormat.primary)
                                .fontWeight(.bold)
                                .foregroundColor(.primary)
                            if let secondary = heightFormat.secondary {
                                Text(secondary)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }

                        Spacer()

                        if abs(height - selectedHeight) < 0.1 {
                            Image(systemName: "checkmark")
                                .foregroundColor(.accentColor)
                        }
                    }
                }
            }
        }
        .navigationTitle("Select Height")
        .navigationBarTitleDisplayMode(.inline)
    }
}

/// A conflict-aware height picker that grays out conflicting combinations
struct ConflictAwareHeightPickerView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var selectedHeight: Double
    let selectedAttempt: Int16
    let session: Session
    let currentJump: Jump?

    private var availableHeights: [Double] {
        session.getGridHeights().sorted(by: >)
    }

    private func isHeightConflicted(_ height: Double) -> Bool {
        guard let jumps = session.jumps?.allObjects as? [Jump] else { return false }

        return jumps.contains { jump in
            jump != currentJump &&
            abs(jump.barHeightCm - height) < 0.1 &&
            jump.attemptIndex == selectedAttempt
        }
    }

    var body: some View {
        List {
            ForEach(availableHeights, id: \.self) { height in
                let isConflicted = isHeightConflicted(height)
                let isSelected = abs(height - selectedHeight) < 0.1

                Button(action: {
                    if !isConflicted {
                        selectedHeight = height
                        dismiss()
                    }
                }) {
                    HStack {
                        let heightFormat = HeightConverter.formatHeightTwoLine(cm: height)
                        VStack(alignment: .leading) {
                            Text(heightFormat.primary)
                                .fontWeight(.bold)
                                .foregroundColor(isConflicted ? .secondary : .primary)
                            if let secondary = heightFormat.secondary {
                                Text(secondary)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }

                        Spacer()

                        if isConflicted {
                            Text("Conflict")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .italic()
                        } else if isSelected {
                            Image(systemName: "checkmark")
                                .foregroundColor(.accentColor)
                        }
                    }
                }
                .disabled(isConflicted)
            }
        }
        .navigationTitle("Select Height")
        .navigationBarTitleDisplayMode(.inline)
    }
}

/// A conflict-aware attempt picker that grays out conflicting combinations
struct ConflictAwareAttemptPickerView: View {
    @Environment(\.dismiss) private var dismiss
    let selectedHeight: Double
    @Binding var selectedAttempt: Int16
    let session: Session
    let currentJump: Jump?

    private let availableAttempts: [Int16] = [1, 2, 3]

    private func isAttemptConflicted(_ attempt: Int16) -> Bool {
        guard let jumps = session.jumps?.allObjects as? [Jump] else { return false }

        return jumps.contains { jump in
            jump != currentJump &&
            abs(jump.barHeightCm - selectedHeight) < 0.1 &&
            jump.attemptIndex == attempt
        }
    }

    var body: some View {
        List {
            ForEach(availableAttempts, id: \.self) { attempt in
                let isConflicted = isAttemptConflicted(attempt)
                let isSelected = attempt == selectedAttempt

                Button(action: {
                    if !isConflicted {
                        selectedAttempt = attempt
                        dismiss()
                    }
                }) {
                    HStack {
                        Text("Attempt #\(attempt)")
                            .fontWeight(.medium)
                            .foregroundColor(isConflicted ? .secondary : .primary)

                        Spacer()

                        if isConflicted {
                            Text("Conflict")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .italic()
                        } else if isSelected {
                            Image(systemName: "checkmark")
                                .foregroundColor(.accentColor)
                        }
                    }
                }
                .disabled(isConflicted)
            }
        }
        .navigationTitle("Select Attempt")
        .navigationBarTitleDisplayMode(.inline)
    }
}

/// A reusable pole selection section with compact display when selected
struct PoleSelectionSection: View {
    @Binding var selectedPole: Pole?
    let athlete: Athlete?

    var body: some View {
        Section(header: Text("Pole")) {
            if let athlete = athlete {
                if let poles = athlete.poles?.allObjects as? [Pole], !poles.isEmpty {
                    let sortedPoles = poles.sorted(by: { $0.order < $1.order })

                    if let selectedPole = selectedPole {
                        // Compact display when pole is selected
                        NavigationLink(destination: PolePickerView(
                            selectedPole: $selectedPole,
                            poles: sortedPoles
                        )) {
                            HStack {
                                VStack(alignment: .leading, spacing: 2) {
                                    // Pole name with color
                                    if let colorHex = selectedPole.color, !colorHex.isEmpty {
                                        Text(selectedPole.name ?? "Unknown Pole")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                            .foregroundColor(Color(hex: colorHex))
                                    } else {
                                        Text(selectedPole.name ?? "Unknown Pole")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                            .foregroundColor(.primary)
                                    }

                                    // Pole details
                                    HStack(spacing: 8) {
                                        if let brand = selectedPole.brand, !brand.isEmpty {
                                            Text(brand)
                                                .font(.caption)
                                                .foregroundColor(.secondary)
                                        }

                                        Text(selectedPole.getFormattedLength())
                                            .font(.caption)
                                            .foregroundColor(.secondary)

                                        Text(selectedPole.getFormattedWeight())
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                }

                                Spacer()
                            }
                        }
                        .foregroundColor(.primary)
                    } else {
                        // When no pole selected
                        NavigationLink(destination: PolePickerView(
                            selectedPole: $selectedPole,
                            poles: sortedPoles
                        )) {
                            HStack {
                                Text("Select Pole")
                                Spacer()
                            }
                        }
                        .foregroundColor(.primary)
                    }
                } else {
                    Text("No poles available. Add poles in the Athlete Profile.")
                        .foregroundColor(.secondary)
                }
            } else {
                Text("No athlete associated with this session.")
                    .foregroundColor(.secondary)
            }
        }
    }
}

/// A dedicated pole picker view for better UX
struct PolePickerView: View {
    @Binding var selectedPole: Pole?
    let poles: [Pole]
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        List {
            // None option
            Button(action: {
                selectedPole = nil
                dismiss()
            }) {
                HStack {
                    Text("None")
                        .foregroundColor(.primary)
                    Spacer()
                    if selectedPole == nil {
                        Image(systemName: "checkmark")
                            .foregroundColor(AppTheme.accentColor)
                    }
                }
            }

            // Pole options
            ForEach(poles, id: \.self) { pole in
                Button(action: {
                    selectedPole = pole
                    dismiss()
                }) {
                    HStack {
                        VStack(alignment: .leading, spacing: 2) {
                            // Pole name with color
                            if let colorHex = pole.color, !colorHex.isEmpty {
                                Text(pole.name ?? "Unknown Pole")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(Color(hex: colorHex))
                            } else {
                                Text(pole.name ?? "Unknown Pole")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.primary)
                            }

                            // Pole details
                            HStack(spacing: 8) {
                                if let brand = pole.brand, !brand.isEmpty {
                                    Text(brand)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }

                                Text(pole.getFormattedLength())
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Text(pole.getFormattedWeight())
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }

                        Spacer()

                        if selectedPole == pole {
                            Image(systemName: "checkmark")
                                .foregroundColor(AppTheme.accentColor)
                        }
                    }
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .navigationTitle("Select Pole")
        .navigationBarTitleDisplayMode(.inline)
    }
}

/// A reusable technical details section
struct TechnicalDetailsSection: View {
    @Binding var showTechnicalDetails: Bool
    @Binding var useBar: Bool
    @Binding var runStartCm: Double
    @Binding var handHoldCm: Double
    @Binding var takeOffStepCm: Double
    @Binding var standardCm: Double

    let sessionType: String?
    let useMetric: Bool

    // Measurement ranges
    let runStartMinCm: Double = 300
    let runStartMaxCm: Double = 4500
    let handHoldMinCm: Double = 200
    let handHoldMaxCm: Double = 500
    let takeOffStepMinCm: Double = 150
    let takeOffStepMaxCm: Double = 300
    let standardMinCm: Double = 50
    let standardMaxCm: Double = 200

    var body: some View {
        Section {
            DisclosureGroup("Technical Details", isExpanded: $showTechnicalDetails) {
                if sessionType?.lowercased() == "practice" {
                    HStack {
                        Text("Bar/Bungee")
                        Spacer()
                        Picker("Bar/Bungee", selection: $useBar) {
                            Text("Bar").tag(true)
                            Text("Bungee").tag(false)
                        }
                        .pickerStyle(SegmentedPickerStyle())
                        .frame(width: 120)
                    }
                }

                InlineTechnicalDetailPicker(
                    selectedValue: $runStartCm,
                    title: "Run Start",
                    minCm: runStartMinCm,
                    maxCm: runStartMaxCm,
                    step: 5,
                    formatImperial: InlineTechnicalDetailPicker.formatFeetInches,
                    description: "Distance from starting point to takeoff"
                )

                InlineTechnicalDetailPicker(
                    selectedValue: $handHoldCm,
                    title: "Hand Hold",
                    minCm: handHoldMinCm,
                    maxCm: handHoldMaxCm,
                    step: 5,
                    formatImperial: InlineTechnicalDetailPicker.formatHandHold,
                    description: "Height of hand grip on pole"
                )

                InlineTechnicalDetailPicker(
                    selectedValue: $takeOffStepCm,
                    title: "Take-Off Step",
                    minCm: takeOffStepMinCm,
                    maxCm: takeOffStepMaxCm,
                    step: 5,
                    formatImperial: InlineTechnicalDetailPicker.formatFeetInches,
                    description: "Distance from takeoff to standard"
                )

                InlineTechnicalDetailPicker(
                    selectedValue: $standardCm,
                    title: "Standard",
                    minCm: standardMinCm,
                    maxCm: standardMaxCm,
                    step: 1,
                    formatImperial: { cm in
                        let inches = cm / 2.54
                        return String(format: "%.0f in", inches)
                    },
                    description: "Height of standard above ground"
                )
            }
        }
    }
}

/// A reusable comment/notes section
struct CommentSection: View {
    @Binding var comment: String

    var body: some View {
        Section(header: Text("Notes")) {
            TextField("Add notes about this jump...", text: $comment, axis: .vertical)
                .lineLimit(3...6)
        }
    }
}
