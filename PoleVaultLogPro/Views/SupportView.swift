import SwiftUI
import MessageUI

struct SupportView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var isShowingMailView = false
    @State private var mailResult: Result<MFMailComposeResult, Error>? = nil
    @State private var showingMailAlert = false

    var body: some View {
        NavigationStack {
            VStack(spacing: 30) {
                // App icon
                Image(AppTheme.jumperIconName)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 100, height: 100)
                    .cornerRadius(22)
                    .overlay(
                        RoundedRectangle(cornerRadius: 22)
                            .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                    )
                    .padding(.top, 40)

                Text("PoleVaultLogPro Support")
                    .font(.title)
                    .fontWeight(.bold)

                VStack(alignment: .leading, spacing: 20) {
                    Text("Need help with PoleVaultLogPro?")
                        .font(.headline)

                    Text("We're here to assist you with any questions, feedback, or issues you may encounter while using the app.")
                        .fixedSize(horizontal: false, vertical: true)

                    Text("Contact us via email:")
                        .font(.headline)
                        .padding(.top, 10)

                    // Email button
                    Button(action: {
                        if MFMailComposeViewController.canSendMail() {
                            isShowingMailView = true
                        } else {
                            // If mail isn't available, provide a fallback
                            UIPasteboard.general.string = "<EMAIL>"
                            showingMailAlert = true
                        }
                    }) {
                        HStack {
                            Image(systemName: "envelope.fill")
                                .foregroundColor(.white)
                            Text("Email Support")
                                .foregroundColor(.white)
                        }
                        .padding()
                        .frame(maxWidth: .infinity)
                        .background(AppTheme.accentColor)
                        .cornerRadius(10)
                    }

                    // Alternative email link
                    HStack {
                        Text("Or email us directly at:")
                        Link("<EMAIL>", destination: URL(string: "mailto:<EMAIL>")!)
                            .foregroundColor(AppTheme.accentColor)
                    }
                    .font(.subheadline)

                    Spacer()

                    // App version
                    HStack {
                        Spacer()
                        VStack {
                            Text("App Version")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            if let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String,
                               let build = Bundle.main.infoDictionary?["CFBundleVersion"] as? String {
                                Text("\(version) (\(build))")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        Spacer()
                    }
                }
                .padding()

                Spacer()
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .sheet(isPresented: $isShowingMailView) {
                MailView(result: $mailResult, subject: "PoleVaultLogPro Support Request", recipients: ["<EMAIL>"], message: "")
            }
            .alert(isPresented: $showingMailAlert) {
                Alert(
                    title: Text("Email Not Available"),
                    message: Text("Your device is not configured to send emails. The support email address has been copied to your clipboard."),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
    }
}

// Mail view using UIViewControllerRepresentable
struct MailView: UIViewControllerRepresentable {
    @Binding var result: Result<MFMailComposeResult, Error>?
    var subject: String
    var recipients: [String]
    var message: String

    func makeUIViewController(context: Context) -> MFMailComposeViewController {
        let viewController = MFMailComposeViewController()
        viewController.mailComposeDelegate = context.coordinator
        viewController.setSubject(subject)
        viewController.setToRecipients(recipients)
        viewController.setMessageBody(message, isHTML: false)
        return viewController
    }

    func updateUIViewController(_ uiViewController: MFMailComposeViewController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, MFMailComposeViewControllerDelegate {
        var parent: MailView

        init(_ parent: MailView) {
            self.parent = parent
        }

        func mailComposeController(_ controller: MFMailComposeViewController, didFinishWith result: MFMailComposeResult, error: Error?) {
            if let error = error {
                parent.result = .failure(error)
            } else {
                parent.result = .success(result)
            }
            controller.dismiss(animated: true)
        }
    }
}

#Preview {
    SupportView()
}
