import SwiftUI
import PhotosUI
import Photos
import AVKit

/// A view for sharing videos with statistics overlays
struct VideoSharingView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.managedObjectContext) private var viewContext

    // Input parameters
    let session: Session
    var jump: Jump? = nil

    // State variables
    @State private var selectedJump: Jump? = nil
    @State private var selectedVideoAsset: String? = nil
    @State private var isProcessing = false
    @State private var processingProgress: Float = 0
    @State private var processedVideoURL: URL? = nil
    @State private var errorMessage: String? = nil
    @State private var showShareSheet = false

    // Preview state
    @State private var showPreview = false
    @State private var previewImage: UIImage?

    // Export options
    @State private var includeJumpStats = true
    @State private var includeSessionInfo = true
    @State private var exportQuality: ExportQuality = .auto
    @State private var useSimpleExport = false  // No overlays option

    // Enum for export quality options
    enum ExportQuality: String, CaseIterable, Identifiable {
        case auto = "Auto (Recommended)"
        case high = "High Quality"
        case medium = "Medium Quality"
        case low = "Low Quality (Fast)"

        var id: String { self.rawValue }

        var presetName: String {
            switch self {
            case .auto:
                return "auto"
            case .high:
                return AVAssetExportPresetHighestQuality
            case .medium:
                return AVAssetExportPresetMediumQuality
            case .low:
                return AVAssetExportPreset1280x720
            }
        }

        var videoQualityPreset: VideoProcessor.VideoQualityPreset {
            switch self {
            case .auto:
                return .auto
            case .high:
                return .highest
            case .medium:
                return .medium
            case .low:
                return .low
            }
        }
    }

    // Fetch jumps for this session
    @FetchRequest private var jumps: FetchedResults<Jump>

    // Initialize with custom fetch request for jumps
    init(session: Session, jump: Jump? = nil) {
        self.session = session
        self.jump = jump

        // Configure the fetch request for jumps
        _jumps = FetchRequest<Jump>(
            sortDescriptors: [
                NSSortDescriptor(keyPath: \Jump.order, ascending: false) // Most recent first
            ],
            predicate: NSPredicate(format: "session == %@", session)
        )
    }

    var body: some View {
        NavigationStack {
            Form {
                // Video selection section
                if jump == nil {
                    jumpSelectionSection
                }

                // Video preview section
                videoPreviewSection

                // Export options section
                exportOptionsSection

                // Action buttons section
                actionButtonsSection
            }
            .navigationTitle("Video Sharing")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                // If a jump was provided, use it
                if let jump = jump {
                    selectedJump = jump
                    // Find the first video for this jump
                    findFirstVideoForJump(jump)
                } else if let firstJump = jumps.first {
                    // Otherwise use the first jump from the session
                    selectedJump = firstJump
                    findFirstVideoForJump(firstJump)
                }
            }
            .sheet(isPresented: $showShareSheet) {
                if let url = processedVideoURL {
                    ShareSheet(activityItems: [url])
                }
            }
            .sheet(isPresented: $showPreview) {
                if let previewImage = previewImage {
                    NavigationStack {
                        VStack {
                            Text("Overlay Preview")
                                .font(.headline)
                                .padding()

                            Image(uiImage: previewImage)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(maxHeight: 500)
                                .cornerRadius(12)
                                .padding()

                            Text("This shows how the overlay will appear on your video")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(.bottom)

                            Spacer()
                        }
                        .toolbar {
                            ToolbarItem(placement: .cancellationAction) {
                                Button("Close") {
                                    showPreview = false
                                }
                            }
                        }
                    }
                }
            }
            .alert(isPresented: Binding<Bool>(
                get: { errorMessage != nil },
                set: { if !$0 { errorMessage = nil } }
            )) {
                Alert(
                    title: Text("Error"),
                    message: Text(errorMessage ?? "An unknown error occurred"),
                    dismissButton: .default(Text("OK"))
                )
            }
        }
    }

    // MARK: - View Components

    private var jumpSelectionSection: some View {
        Section(header: Text("Select Jump")) {
            if jumps.isEmpty {
                Text("No jumps available for this session")
                    .foregroundColor(.secondary)
            } else {
                Picker("Jump", selection: $selectedJump) {
                    ForEach(jumps) { jump in
                        JumpPickerRow(jump: jump)
                            .tag(jump as Jump?)
                    }
                }
                .pickerStyle(.navigationLink)
                .onChange(of: selectedJump) { _, newJump in
                    if let jump = newJump {
                        findFirstVideoForJump(jump)
                    } else {
                        selectedVideoAsset = nil
                    }
                }
            }
        }
    }

    private var videoPreviewSection: some View {
        Section(header: Text("Video Preview")) {
            if isProcessing {
                VStack(spacing: 12) {
                    ProgressView(value: processingProgress, total: 1.0)
                        .progressViewStyle(LinearProgressViewStyle())

                    Text("Processing video: \(Int(processingProgress * 100))%")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding()
            } else if let assetIdentifier = selectedVideoAsset {
                VStack(spacing: 8) {
                    PHAssetThumbnailView(assetIdentifier: assetIdentifier)
                        .frame(height: 200)
                        .cornerRadius(8)

                    if let jump = selectedJump {
                        Text("Height: \(formatHeight(jump.barHeightCm)) • \(jump.result?.capitalized ?? "Unknown")")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding(.vertical, 8)
            } else {
                VStack(spacing: 12) {
                    Image(systemName: "video.slash")
                        .font(.system(size: 40))
                        .foregroundColor(.secondary)
                        .padding()

                    Text("No video available")
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding()
            }
        }
    }

    private var exportOptionsSection: some View {
        Section(header: Text("Export Options")) {
            Toggle("Include Jump Statistics", isOn: $includeJumpStats)
                .disabled(useSimpleExport)

            Toggle("Include Session Info", isOn: $includeSessionInfo)
                .disabled(useSimpleExport)

            Picker("Export Quality", selection: $exportQuality) {
                ForEach(ExportQuality.allCases) { quality in
                    Text(quality.rawValue).tag(quality)
                }
            }

            Toggle("No Overlays", isOn: $useSimpleExport)
                .foregroundColor(.orange)
                .onChange(of: useSimpleExport) { _, newValue in
                    if newValue {
                        // Disable stats and session info when no overlays is selected
                        includeJumpStats = false
                        includeSessionInfo = false
                    } else {
                        // Re-enable them when overlays are enabled
                        includeJumpStats = true
                        includeSessionInfo = true
                    }
                }

            // Information about watermark
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.blue)
                Text("App logo and name will always be included")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.top, 4)
        }
    }

    private var actionButtonsSection: some View {
        Section {
            // Preview button
            Button(action: generatePreview) {
                HStack {
                    Spacer()
                    Label("Preview Overlay", systemImage: "eye")
                    Spacer()
                }
            }
            .disabled(selectedVideoAsset == nil || useSimpleExport)
            .foregroundColor(.orange)

            Button(action: processAndShareVideo) {
                HStack {
                    Spacer()
                    Label("Process & Share Video", systemImage: "square.and.arrow.up")
                    Spacer()
                }
            }
            .disabled(selectedVideoAsset == nil || isProcessing)

            if processedVideoURL != nil {
                Button(action: {
                    showShareSheet = true
                }) {
                    HStack {
                        Spacer()
                        Label("Share Again", systemImage: "arrow.triangle.2.circlepath")
                        Spacer()
                    }
                }
                .foregroundColor(.blue)
            }
        }
    }

    // MARK: - Helper Methods

    /// Finds the first video for a jump
    private func findFirstVideoForJump(_ jump: Jump) {
        let mediaItems = jump.getMediaItems()
        let videoItem = mediaItems.first { $0.type == .video }

        selectedVideoAsset = videoItem?.assetIdentifier
    }

    /// Processes and shares the selected video
    private func processAndShareVideo() {
        guard let jump = selectedJump, let assetIdentifier = selectedVideoAsset else {
            errorMessage = "No video selected"
            return
        }

        // Reset state
        isProcessing = true
        processingProgress = 0
        processedVideoURL = nil
        errorMessage = nil

        // Set up a fallback progress timer in case the export session doesn't report progress
        var fallbackTimer: Timer?
        var lastProgressUpdate = Date()

        fallbackTimer = Timer.scheduledTimer(withTimeInterval: 0.25, repeats: true) { timer in

            // If we haven't received a progress update in 1 second, simulate progress
            if Date().timeIntervalSince(lastProgressUpdate) > 1.0 && self.processingProgress < 0.95 {
                // Simulate progress that accelerates over time but never reaches 1.0
                let newProgress = min(self.processingProgress + 0.01, 0.95)
                self.processingProgress = newProgress
            }

            // If we're no longer processing, invalidate the timer
            if !self.isProcessing {
                timer.invalidate()
            }
        }

        // Create export options
        var exportOptions: VideoProcessor.VideoExportOptions

        if useSimpleExport {
            // Use simple export preset for testing
            exportOptions = VideoProcessor.VideoExportOptions.simpleExport
        } else {
            // Use custom options
            exportOptions = VideoProcessor.VideoExportOptions()
            exportOptions.includeJumpStats = includeJumpStats
            exportOptions.includeSessionInfo = includeSessionInfo
            exportOptions.includeAppWatermark = true // Always include watermark
            exportOptions.quality = exportQuality.videoQualityPreset
            exportOptions.outputFileType = .mp4
        }

        // Process the video
        VideoProcessor.shared.processVideoWithOverlay(
            assetIdentifier: assetIdentifier,
            jump: jump,
            options: exportOptions,
            progressHandler: { progress in
                self.processingProgress = progress
                lastProgressUpdate = Date() // Update the timestamp when we get a real progress update
            },
            completion: { result in
                self.isProcessing = false
                fallbackTimer?.invalidate() // Make sure to invalidate the timer

                // Ensure progress shows 100% on completion
                if case .success = result {
                    self.processingProgress = 1.0
                }

                switch result {
                case .success(let tempURL):
                    // Move the file from temporary directory to documents directory for sharing
                    let moveResult = VideoProcessor.shared.moveToDocumentsForSharing(tempURL: tempURL)

                    switch moveResult {
                    case .success(let documentsURL):
                        // Use the URL from the documents directory for sharing
                        self.processedVideoURL = documentsURL
                        self.showShareSheet = true
                    case .failure(let error):
                        self.errorMessage = "Error preparing video for sharing: \(error.localizedDescription)"
                    }

                case .failure(let error):
                    self.errorMessage = "Error processing video: \(error.localizedDescription)"
                }
            }
        )
    }

    /// Generates a preview of the overlay
    private func generatePreview() {
        guard let jump = selectedJump, let assetIdentifier = selectedVideoAsset else {
            errorMessage = "No video selected"
            return
        }

        Task {
            do {
                // Get the video thumbnail
                let thumbnail = await getVideoThumbnail(assetIdentifier: assetIdentifier)

                // Create overlay text
                let overlayText = createOverlayText(from: jump)

                // Create preview image with overlay
                let preview = createPreviewImage(thumbnail: thumbnail, overlayText: overlayText)

                await MainActor.run {
                    self.previewImage = preview
                    self.showPreview = true
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "Failed to generate preview: \(error.localizedDescription)"
                }
            }
        }
    }

    /// Gets a thumbnail from the video asset with proper aspect ratio
    private func getVideoThumbnail(assetIdentifier: String) async -> UIImage? {
        return await withCheckedContinuation { continuation in
            // First try to find the asset as a shared asset
            var asset = SharedMediaManager.shared.fetchSharedAsset(with: assetIdentifier)

            // If not found as a shared asset, try as a local asset
            if asset == nil {
                let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetIdentifier], options: nil)
                asset = fetchResult.firstObject
            }

            guard let asset = asset else {
                continuation.resume(returning: nil)
                return
            }

            let options = PHImageRequestOptions()
            options.deliveryMode = .highQualityFormat
            options.isNetworkAccessAllowed = true
            options.resizeMode = .exact

            // Use the actual video dimensions for proper aspect ratio
            let targetSize: CGSize
            if asset.pixelWidth > asset.pixelHeight {
                // Landscape video
                targetSize = CGSize(width: 600, height: 400)
            } else {
                // Portrait video
                targetSize = CGSize(width: 400, height: 600)
            }

            PHImageManager.default().requestImage(
                for: asset,
                targetSize: targetSize,
                contentMode: .aspectFit,
                options: options
            ) { image, info in
                continuation.resume(returning: image)
            }
        }
    }

    /// Creates overlay text from jump data
    private func createOverlayText(from jump: Jump) -> String {
        var textLines: [String] = []

        if includeJumpStats {
            let sessionTitle = jump.session?.title ?? "Untitled Session"
            let heightString = jump.barHeightCm > 0 ? formatHeight(jump.barHeightCm) : "Unknown Height"
            let attemptString = "Attempt \(jump.attemptIndex)"
            let resultString = jump.result?.capitalized ?? "Unknown"
            let barBungeeString = jump.useBar ? "Bar" : "Bungee"
            let poleName = jump.pole?.name ?? "No Pole"
            let runStart = jump.runStartCm > 0 ? String(format: "%.1f", jump.runStartCm / 100.0) : "0.0"
            let handHold = jump.handHoldCm > 0 ? String(format: "%.1f", jump.handHoldCm / 100.0) : "0.0"
            let takeOffStep = jump.takeOffStepCm > 0 ? String(format: "%.1f", jump.takeOffStepCm / 100.0) : "0.0"
            let standard = jump.standardCm > 0 ? String(format: "%.1f", jump.standardCm / 100.0) : "0.0"

            textLines.append(sessionTitle)
            textLines.append(heightString)
            textLines.append("\(attemptString) - \(resultString)")
            textLines.append(barBungeeString)
            textLines.append(poleName)
            textLines.append(runStart)
            textLines.append(handHold)
            textLines.append(takeOffStep)
            textLines.append(standard)
        }

        if includeSessionInfo {
            let sessionDate = jump.session?.date != nil ? Date(timeIntervalSinceReferenceDate: jump.session!.date) : Date()
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            formatter.timeStyle = .short
            let dateString = formatter.string(from: sessionDate)
            textLines.append(dateString)
        }

        textLines.append("PoleVaultLogPro")

        return textLines.joined(separator: "\n")
    }

    /// Creates a preview image with overlay text using the same styled rendering as video export
    private func createPreviewImage(thumbnail: UIImage?, overlayText: String) -> UIImage {
        // Use the thumbnail's actual size, or default to portrait if no thumbnail
        let canvasSize = thumbnail?.size ?? CGSize(width: 400, height: 600)

        UIGraphicsBeginImageContextWithOptions(canvasSize, false, 1.0)
        defer { UIGraphicsEndImageContext() }

        guard let context = UIGraphicsGetCurrentContext() else {
            return UIImage()
        }

        // Draw the thumbnail as background
        if let thumbnail = thumbnail {
            thumbnail.draw(in: CGRect(origin: .zero, size: canvasSize))
        } else {
            // Draw a gray background if no thumbnail
            UIColor.gray.setFill()
            UIRectFill(CGRect(origin: .zero, size: canvasSize))
        }

        // Use the same styled overlay rendering as the video processor
        let overlayData = parseOverlayTextForPreview(overlayText)
        renderStyledOverlayForPreview(context: context, canvasSize: canvasSize, overlayData: overlayData)

        return UIGraphicsGetImageFromCurrentImageContext() ?? UIImage()
    }

    private func parseOverlayTextForPreview(_ text: String) -> PreviewOverlayData {
        let lines = text.components(separatedBy: "\n")

        var sessionTitle = ""
        var height = ""
        var attemptResult = ""
        var sessionInfo = ""
        var watermark = ""
        var useBar = true
        var poleName = ""
        var attemptNumber = ""
        var runStart = ""
        var handHold = ""
        var takeOffStep = ""
        var standard = ""

        for (index, line) in lines.enumerated() {
            if line.contains("Attempt") {
                attemptResult = line
                // Extract attempt number
                let components = line.components(separatedBy: " ")
                if components.count >= 2 {
                    attemptNumber = components[1]
                }
            } else if line.contains("m") || line.contains("ft") {
                height = line
            } else if line.contains("PoleVaultLogPro") {
                watermark = line
            } else if line.contains(":") || line.contains("AM") || line.contains("PM") {
                sessionInfo = line
            } else if line.lowercased() == "bar" {
                useBar = true
            } else if line.lowercased() == "bungee" {
                useBar = false
            } else if index == 4 { // Pole name is 5th line
                poleName = line
            } else if index == 5 { // Run start is 6th line
                runStart = line
            } else if index == 6 { // Hand hold is 7th line
                handHold = line
            } else if index == 7 { // Take off step is 8th line
                takeOffStep = line
            } else if index == 8 { // Standard is 9th line
                standard = line
            } else if !line.isEmpty && sessionTitle.isEmpty {
                sessionTitle = line
            }
        }

        return PreviewOverlayData(
            sessionTitle: sessionTitle,
            height: height,
            attemptResult: attemptResult,
            sessionInfo: sessionInfo,
            watermark: watermark,
            useBar: useBar,
            poleName: poleName,
            attemptNumber: attemptNumber,
            runStart: runStart,
            handHold: handHold,
            takeOffStep: takeOffStep,
            standard: standard
        )
    }

    private func renderStyledOverlayForPreview(context: CGContext, canvasSize: CGSize, overlayData: PreviewOverlayData) {
        // Top Left: Title of Season - Height
        renderTopLeftOverlayForPreview(context: context, canvasSize: canvasSize, overlayData: overlayData)

        // Top Right: Icon - AppName
        renderTopRightOverlayForPreview(context: context, canvasSize: canvasSize, overlayData: overlayData)

        // Bottom Left: Pass/Make/Miss | Bar/Bungee | PoleName | Attempt#
        renderBottomLeftOverlayForPreview(context: context, canvasSize: canvasSize, overlayData: overlayData)

        // Bottom Center: Date of Jump
        renderBottomCenterOverlayForPreview(context: context, canvasSize: canvasSize, overlayData: overlayData)

        // Bottom Right: Run | Grip | Step | Std
        renderBottomRightOverlayForPreview(context: context, canvasSize: canvasSize, overlayData: overlayData)
    }

    // MARK: - New Four-Section Preview Overlay Rendering

    private func renderTopLeftOverlayForPreview(context: CGContext, canvasSize: CGSize, overlayData: PreviewOverlayData) {
        let padding: CGFloat = 20
        let overlayWidth: CGFloat = min(300, canvasSize.width * 0.4)
        let overlayHeight: CGFloat = 60

        // Background
        let backgroundRect = CGRect(x: padding, y: padding, width: overlayWidth, height: overlayHeight)
        context.setFillColor(UIColor.black.withAlphaComponent(0.7).cgColor)
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 8)
        context.addPath(backgroundPath.cgPath)
        context.fillPath()

        // Session Title
        let titleAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.boldSystemFont(ofSize: 16),
            .foregroundColor: UIColor.white
        ]
        let titleText = NSAttributedString(string: overlayData.sessionTitle, attributes: titleAttributes)
        let titlePoint = CGPoint(x: padding + 12, y: padding + 8)
        titleText.draw(at: titlePoint)

        // Height
        let heightAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.boldSystemFont(ofSize: 20),
            .foregroundColor: UIColor.systemBlue
        ]
        let heightText = NSAttributedString(string: overlayData.height, attributes: heightAttributes)
        let heightPoint = CGPoint(x: padding + 12, y: padding + 32)
        heightText.draw(at: heightPoint)
    }

    private func renderTopRightOverlayForPreview(context: CGContext, canvasSize: CGSize, overlayData: PreviewOverlayData) {
        let padding: CGFloat = 20
        let overlayWidth: CGFloat = 180
        let overlayHeight: CGFloat = 40
        let xPosition = canvasSize.width - overlayWidth - padding

        // Background
        let backgroundRect = CGRect(x: xPosition, y: padding, width: overlayWidth, height: overlayHeight)
        context.setFillColor(UIColor.black.withAlphaComponent(0.6).cgColor)
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 6)
        context.addPath(backgroundPath.cgPath)
        context.fillPath()

        // App Name
        let appNameAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 14, weight: .medium),
            .foregroundColor: UIColor.white.withAlphaComponent(0.9)
        ]
        let appNameText = NSAttributedString(string: "PoleVaultLogPro", attributes: appNameAttributes)
        let appNamePoint = CGPoint(x: xPosition + 12, y: padding + 12)
        appNameText.draw(at: appNamePoint)
    }

    private func renderBottomLeftOverlayForPreview(context: CGContext, canvasSize: CGSize, overlayData: PreviewOverlayData) {
        let padding: CGFloat = 20
        let overlayWidth: CGFloat = min(350, canvasSize.width * 0.45)
        let overlayHeight: CGFloat = 60
        let yPosition = canvasSize.height - overlayHeight - padding

        // Background
        let backgroundRect = CGRect(x: padding, y: yPosition, width: overlayWidth, height: overlayHeight)
        context.setFillColor(UIColor.black.withAlphaComponent(0.7).cgColor)
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 8)
        context.addPath(backgroundPath.cgPath)
        context.fillPath()

        // Extract result from attemptResult
        let result = overlayData.attemptResult.components(separatedBy: " - ").last ?? "Unknown"
        let barBungeeString = overlayData.useBar ? "Bar" : "Bungee"

        // Create the combined text: Pass/Make/Miss | Bar/Bungee | PoleName | Attempt#
        let combinedText = "\(result) | \(barBungeeString) | \(overlayData.poleName) | #\(overlayData.attemptNumber)"

        let textAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 14, weight: .medium),
            .foregroundColor: UIColor.white
        ]
        let textString = NSAttributedString(string: combinedText, attributes: textAttributes)
        let textPoint = CGPoint(x: padding + 12, y: yPosition + 20)
        textString.draw(at: textPoint)
    }

    private func renderBottomCenterOverlayForPreview(context: CGContext, canvasSize: CGSize, overlayData: PreviewOverlayData) {
        let overlayWidth: CGFloat = 200
        let overlayHeight: CGFloat = 40
        let padding: CGFloat = 20
        let xPosition = (canvasSize.width - overlayWidth) / 2
        let yPosition = canvasSize.height - overlayHeight - padding

        // Background
        let backgroundRect = CGRect(x: xPosition, y: yPosition, width: overlayWidth, height: overlayHeight)
        context.setFillColor(UIColor.black.withAlphaComponent(0.7).cgColor)
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 8)
        context.addPath(backgroundPath.cgPath)
        context.fillPath()

        // Date
        let dateAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 14, weight: .medium),
            .foregroundColor: UIColor.white
        ]
        let dateText = NSAttributedString(string: overlayData.sessionInfo, attributes: dateAttributes)
        let dateSize = dateText.size()
        let datePoint = CGPoint(
            x: xPosition + (overlayWidth - dateSize.width) / 2,
            y: yPosition + (overlayHeight - dateSize.height) / 2
        )
        dateText.draw(at: datePoint)
    }

    private func renderBottomRightOverlayForPreview(context: CGContext, canvasSize: CGSize, overlayData: PreviewOverlayData) {
        let padding: CGFloat = 20
        let overlayWidth: CGFloat = min(250, canvasSize.width * 0.35)
        let overlayHeight: CGFloat = 60
        let xPosition = canvasSize.width - overlayWidth - padding
        let yPosition = canvasSize.height - overlayHeight - padding

        // Background
        let backgroundRect = CGRect(x: xPosition, y: yPosition, width: overlayWidth, height: overlayHeight)
        context.setFillColor(UIColor.black.withAlphaComponent(0.7).cgColor)
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 8)
        context.addPath(backgroundPath.cgPath)
        context.fillPath()

        // Technical measurements: Run | Grip | Step | Std
        let techText = "Run: \(overlayData.runStart) | Grip: \(overlayData.handHold) | Step: \(overlayData.takeOffStep) | Std: \(overlayData.standard)"

        let textAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 12, weight: .medium),
            .foregroundColor: UIColor.white
        ]
        let textString = NSAttributedString(string: techText, attributes: textAttributes)
        let textPoint = CGPoint(x: xPosition + 12, y: yPosition + 20)
        textString.draw(at: textPoint)
    }

    private func renderStatsOverlayForPreview(context: CGContext, canvasSize: CGSize, overlayData: PreviewOverlayData) {
        let padding: CGFloat = 20
        let overlayWidth: CGFloat = min(300, canvasSize.width - 40)
        let overlayHeight: CGFloat = 100

        // Background with rounded corners and transparency
        let backgroundRect = CGRect(x: padding, y: padding, width: overlayWidth, height: overlayHeight)
        context.setFillColor(UIColor.black.withAlphaComponent(0.7).cgColor)
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 12)
        context.addPath(backgroundPath.cgPath)
        context.fillPath()

        // Session title
        var yOffset: CGFloat = padding + 10
        if !overlayData.sessionTitle.isEmpty {
            let titleFont = UIFont.boldSystemFont(ofSize: 14)
            let titleAttributes: [NSAttributedString.Key: Any] = [
                .font: titleFont,
                .foregroundColor: UIColor.white
            ]
            let titleRect = CGRect(x: padding + 10, y: yOffset, width: overlayWidth - 20, height: 20)
            (overlayData.sessionTitle as NSString).draw(in: titleRect, withAttributes: titleAttributes)
            yOffset += 25
        }

        // Height with accent color
        if !overlayData.height.isEmpty {
            let heightFont = UIFont.boldSystemFont(ofSize: 18)
            let heightAttributes: [NSAttributedString.Key: Any] = [
                .font: heightFont,
                .foregroundColor: UIColor.systemBlue
            ]
            let heightRect = CGRect(x: padding + 10, y: yOffset, width: overlayWidth - 20, height: 25)
            (overlayData.height as NSString).draw(in: heightRect, withAttributes: heightAttributes)
            yOffset += 30
        }

        // Attempt and result badges
        if !overlayData.attemptResult.isEmpty {
            let components = overlayData.attemptResult.components(separatedBy: " - ")
            let attempt = components.first ?? ""
            let result = components.count > 1 ? components[1] : ""

            var xOffset: CGFloat = padding + 10

            // Attempt badge
            if !attempt.isEmpty {
                let attemptFont = UIFont.systemFont(ofSize: 10, weight: .medium)
                let attemptSize = (attempt as NSString).size(withAttributes: [.font: attemptFont])
                let attemptBadgeRect = CGRect(x: xOffset, y: yOffset, width: attemptSize.width + 8, height: 16)

                context.setFillColor(UIColor.gray.withAlphaComponent(0.6).cgColor)
                let attemptBadgePath = UIBezierPath(roundedRect: attemptBadgeRect, cornerRadius: 3)
                context.addPath(attemptBadgePath.cgPath)
                context.fillPath()

                let attemptAttributes: [NSAttributedString.Key: Any] = [
                    .font: attemptFont,
                    .foregroundColor: UIColor.white
                ]
                let attemptTextRect = CGRect(x: xOffset + 4, y: yOffset + 2, width: attemptSize.width, height: 12)
                (attempt as NSString).draw(in: attemptTextRect, withAttributes: attemptAttributes)

                xOffset += attemptBadgeRect.width + 8
            }

            // Result badge
            if !result.isEmpty {
                let resultFont = UIFont.systemFont(ofSize: 10, weight: .medium)
                let resultSize = (result as NSString).size(withAttributes: [.font: resultFont])
                let resultBadgeRect = CGRect(x: xOffset, y: yOffset, width: resultSize.width + 8, height: 16)

                let resultColor: UIColor
                switch result.lowercased() {
                case "made", "make":
                    resultColor = UIColor.systemGreen
                case "miss":
                    resultColor = UIColor.systemRed
                case "pass":
                    resultColor = UIColor.systemOrange
                default:
                    resultColor = UIColor.gray
                }

                context.setFillColor(resultColor.withAlphaComponent(0.6).cgColor)
                let resultBadgePath = UIBezierPath(roundedRect: resultBadgeRect, cornerRadius: 3)
                context.addPath(resultBadgePath.cgPath)
                context.fillPath()

                let resultAttributes: [NSAttributedString.Key: Any] = [
                    .font: resultFont,
                    .foregroundColor: UIColor.white
                ]
                let resultTextRect = CGRect(x: xOffset + 4, y: yOffset + 2, width: resultSize.width, height: 12)
                (result as NSString).draw(in: resultTextRect, withAttributes: resultAttributes)
            }
        }
    }

    private func renderSessionInfoOverlayForPreview(context: CGContext, canvasSize: CGSize, sessionInfo: String) {
        let padding: CGFloat = 20
        let overlayWidth: CGFloat = min(250, canvasSize.width - 40)
        let overlayHeight: CGFloat = 30
        let yPosition = canvasSize.height - overlayHeight - padding

        // Background
        let backgroundRect = CGRect(x: padding, y: yPosition, width: overlayWidth, height: overlayHeight)
        context.setFillColor(UIColor.black.withAlphaComponent(0.7).cgColor)
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 6)
        context.addPath(backgroundPath.cgPath)
        context.fillPath()

        // Session info text
        let infoFont = UIFont.systemFont(ofSize: 11)
        let infoAttributes: [NSAttributedString.Key: Any] = [
            .font: infoFont,
            .foregroundColor: UIColor.white
        ]
        let textRect = CGRect(x: padding + 8, y: yPosition + 8, width: overlayWidth - 16, height: 14)
        (sessionInfo as NSString).draw(in: textRect, withAttributes: infoAttributes)
    }

    private func renderWatermarkOverlayForPreview(context: CGContext, canvasSize: CGSize, watermark: String) {
        let padding: CGFloat = 20
        let overlayWidth: CGFloat = 140
        let overlayHeight: CGFloat = 24
        let xPosition = canvasSize.width - overlayWidth - padding

        // Background
        let backgroundRect = CGRect(x: xPosition, y: padding, width: overlayWidth, height: overlayHeight)
        context.setFillColor(UIColor.black.withAlphaComponent(0.5).cgColor)
        let backgroundPath = UIBezierPath(roundedRect: backgroundRect, cornerRadius: 4)
        context.addPath(backgroundPath.cgPath)
        context.fillPath()

        // Watermark text
        let watermarkFont = UIFont.systemFont(ofSize: 10, weight: .medium)
        let watermarkAttributes: [NSAttributedString.Key: Any] = [
            .font: watermarkFont,
            .foregroundColor: UIColor.white.withAlphaComponent(0.8)
        ]
        let textRect = CGRect(x: xPosition + 6, y: padding + 6, width: overlayWidth - 12, height: 12)
        (watermark as NSString).draw(in: textRect, withAttributes: watermarkAttributes)
    }

    private struct PreviewOverlayData {
        let sessionTitle: String
        let height: String
        let attemptResult: String
        let sessionInfo: String
        let watermark: String
        let useBar: Bool
        let poleName: String
        let attemptNumber: String
        let runStart: String
        let handHold: String
        let takeOffStep: String
        let standard: String
    }

    /// Formats height based on user settings
    private func formatHeight(_ heightCm: Double) -> String {
        if AppTheme.useMetricSystem {
            return HeightConverter.cmToMetersString(heightCm)
        } else {
            return HeightConverter.cmToFeetInchesString(heightCm)
        }
    }
}

/// A row view for displaying a jump in a picker
struct JumpPickerRow: View {
    let jump: Jump

    var body: some View {
        HStack {
            // Height
            Text(formatHeight(jump.barHeightCm))
                .font(.headline)

            // Result badge
            Text(jump.result?.capitalized ?? "Unknown")
                .font(.caption)
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(resultColor.opacity(0.6))
                .foregroundColor(.white)
                .cornerRadius(4)

            Spacer()

            // Media indicator
            if hasVideo {
                Image(systemName: "video.fill")
                    .foregroundColor(.blue)
            }
        }
    }

    // Check if the jump has a video attached
    private var hasVideo: Bool {
        let mediaItems = jump.getMediaItems()
        return mediaItems.contains(where: { $0.type == .video })
    }

    // Get color based on result
    private var resultColor: Color {
        switch jump.result {
        case "make":
            return .green
        case "miss":
            return .red
        case "pass":
            return .gray
        default:
            return .gray
        }
    }

    // Format height based on user settings
    private func formatHeight(_ heightCm: Double) -> String {
        if AppTheme.useMetricSystem {
            return HeightConverter.cmToMetersString(heightCm)
        } else {
            return HeightConverter.cmToFeetInchesString(heightCm)
        }
    }
}

struct PHAssetThumbnailView: View {
    let assetIdentifier: String
    @State private var thumbnail: UIImage?
    @State private var isLoading = true

    var body: some View {
        ZStack {
            if let thumbnail = thumbnail {
                Image(uiImage: thumbnail)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .overlay(
                        Image(systemName: "play.circle.fill")
                            .font(.system(size: 20))
                            .foregroundColor(.white)
                            .shadow(radius: 2)
                    )
            } else if isLoading {
                ProgressView()
            } else {
                Color.gray.opacity(0.3)
                    .overlay(
                        Image(systemName: "video.slash.fill")
                            .font(.system(size: 20))
                            .foregroundColor(.gray)
                    )
            }
        }
        .onAppear {
            loadThumbnail()
        }
    }

    private func loadThumbnail() {
        // First try to find the asset as a shared asset
        var asset = SharedMediaManager.shared.fetchSharedAsset(with: assetIdentifier)

        // If not found as a shared asset, try as a local asset
        if asset == nil {
            let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [assetIdentifier], options: nil)
            asset = fetchResult.firstObject
        }

        guard let asset = asset else {
            isLoading = false
            return
        }

        let options = PHImageRequestOptions()
        options.deliveryMode = .opportunistic
        options.isNetworkAccessAllowed = true
        options.resizeMode = .exact

        PHImageManager.default().requestImage(
            for: asset,
            targetSize: CGSize(width: 120, height: 120),
            contentMode: .aspectFill,
            options: options
        ) { image, info in
            DispatchQueue.main.async {
                self.thumbnail = image
                self.isLoading = false
            }
        }
    }
}

struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        // Create a document interaction controller for better file handling
        let activityItems = self.activityItems.map { item -> Any in
            // If the item is a URL, create a proper file URL for sharing
            if let url = item as? URL {
                // Create a document interaction controller to get the proper UTI
                let interaction = UIDocumentInteractionController(url: url)

                // Log the URL and UTI for debugging
                print("📹 DEBUG: Sharing URL: \(url.path)")
                print("📹 DEBUG: UTI: \(interaction.uti ?? "unknown")")

                return url
            }
            return item
        }

        // Create the activity view controller with the processed items
        let controller = UIActivityViewController(
            activityItems: activityItems,
            applicationActivities: nil
        )

        // Configure the controller to handle errors
        controller.completionWithItemsHandler = { (activityType, completed, returnedItems, error) in
            if let error = error {
                print("📹 ERROR: Sharing failed: \(error.localizedDescription)")
            } else if completed {
                print("📹 DEBUG: Sharing completed successfully")
            } else {
                print("📹 DEBUG: Sharing cancelled")
            }
        }

        return controller
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}
