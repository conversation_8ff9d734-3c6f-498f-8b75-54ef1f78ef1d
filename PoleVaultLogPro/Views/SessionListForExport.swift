import SwiftUI

struct SessionListForExport: View {
    @Environment(\.managedObjectContext) private var viewContext

    @FetchRequest(
        sortDescriptors: [NSSortDescriptor(keyPath: \Session.date, ascending: false)],
        animation: .default)
    private var sessions: FetchedResults<Session>

    @State private var showingExportSession = false
    @State private var selectedSession: Session?
    @State private var showingVideoSharing = false

    var body: some View {
        if sessions.isEmpty {
            VStack(spacing: 20) {
                Image(AppTheme.poleVaultIconName)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 80, height: 80)
                    .foregroundColor(AppTheme.accentColor)

                Text("No Sessions to Export")
                    .font(.title2)
                    .fontWeight(.bold)

                Text("Create sessions in the Log tab first")
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }
            .padding(.top, 40)
            .padding(.horizontal)
            .frame(maxWidth: .infinity, alignment: .center)
        } else {
            ForEach(sessions) { session in
                NavigationLink(destination: ExportSessionView(session: session)) {
                    SessionExportRow(session: session)
                }
                .swipeActions(edge: .trailing) {
                    Button {
                        selectedSession = session
                        showingVideoSharing = true
                    } label: {
                        Label("Share Videos", systemImage: "video")
                    }
                    .tint(.blue)
                }
            }
            .sheet(isPresented: $showingVideoSharing) {
                if let session = selectedSession {
                    VideoSharingView(session: session)
                }
            }
        }
    }
}

struct SessionExportRow: View {
    let session: Session

    // Fetch jumps for this session
    @FetchRequest private var jumps: FetchedResults<Jump>

    init(session: Session) {
        self.session = session

        // Configure the fetch request for jumps
        _jumps = FetchRequest<Jump>(
            sortDescriptors: [NSSortDescriptor(keyPath: \Jump.order, ascending: true)],
            predicate: NSPredicate(format: "session == %@", session)
        )
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(session.title ?? "Untitled Session")
                    .font(.headline)

                Spacer()

                if session.bestHeightCm > 0 {
                    HStack(spacing: 4) {
                        // Primary unit (based on user preference)
                        Text(bestHeightString)
                            .font(.subheadline)
                            .fontWeight(.medium)

                        // Secondary unit
                        Text("•")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        if AppTheme.useMetricSystem {
                            Text(HeightConverter.cmToFeetInchesString(session.bestHeightCm))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        } else {
                            Text(HeightConverter.cmToMetersString(session.bestHeightCm))
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(AppTheme.accentColorLight.opacity(0.2))
                    .cornerRadius(8)
                }
            }

            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    HStack(spacing: 6) {
                        // Date
                        Text(formattedDate)
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        // Type badge
                        if let type = session.type {
                            Text(type)
                                .font(.caption)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(type == "Practice" ? Color.gray.opacity(0.2) : Color.orange.opacity(0.2))
                                .cornerRadius(4)
                        }
                    }

                    // Location if available
                    if let location = session.location, !location.isEmpty {
                        Text(location)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                // Jump statistics and media indicator
                if jumps.count > 0 {
                    HStack(spacing: 8) {
                        // Media indicator
                        if hasMedia {
                            Image(systemName: hasVideo ? "video.fill" : "photo.fill")
                                .foregroundColor(.blue)
                                .font(.caption)
                        }

                        HStack(spacing: 4) {
                            Label("\(jumps.filter { $0.result == "make" }.count)", systemImage: "checkmark.circle.fill")
                                .foregroundColor(.green)
                                .font(.caption)
                                .labelStyle(CompactLabelStyle())

                            Label("\(jumps.filter { $0.result == "miss" }.count)", systemImage: "xmark.circle.fill")
                                .foregroundColor(.red)
                                .font(.caption)
                                .labelStyle(CompactLabelStyle())

                            Label("\(jumps.filter { $0.result == "pass" }.count)", systemImage: "arrow.right.circle.fill")
                                .foregroundColor(.gray)
                                .font(.caption)
                                .labelStyle(CompactLabelStyle())
                        }
                    }
                }
            }
        }
        .padding(.vertical, 4)
    }

    private var formattedDate: String {
        let date = Date(timeIntervalSinceReferenceDate: session.date)
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: date)
    }

    private var bestHeightString: String {
        if AppTheme.useMetricSystem {
            return HeightConverter.cmToMetersString(session.bestHeightCm)
        } else {
            return HeightConverter.cmToFeetInchesString(session.bestHeightCm)
        }
    }

    private var hasMedia: Bool {
        // Get all jumps for this session
        let jumps = session.jumps?.allObjects as? [Jump] ?? []

        // Check if any jump has media
        for jump in jumps {
            if !jump.getMediaItemsAsStructs().isEmpty {
                return true
            }
        }

        return false
    }

    private var hasVideo: Bool {
        // Get all jumps for this session
        let jumps = session.jumps?.allObjects as? [Jump] ?? []

        // Check if any jump has video media
        for jump in jumps {
            let mediaItems = jump.getMediaItemsAsStructs()
            if mediaItems.contains(where: { $0.type == .video }) {
                return true
            }
        }

        return false
    }
}

#Preview {
    SessionListForExport()
        .environment(\.managedObjectContext, PersistenceController(inMemory: true).container.viewContext)
}
