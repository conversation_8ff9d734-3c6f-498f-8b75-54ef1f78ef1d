import SwiftUI
import PhotosUI
import CoreData

struct JumpViewEdit: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss

    // Use the global AppTheme.useMetricSystem directly
    private var useMetric: Bool { AppTheme.useMetricSystem }

    // The jump being edited
    @ObservedObject var jump: Jump
    let onSave: ((Jump) -> Void)?

    // Form state
    @State private var barHeightCm: Double
    @State private var result: String
    @State private var comment: String
    @State private var useBar: Bool
    @State private var runStartCm: Double
    @State private var handHoldCm: Double
    @State private var takeOffStepCm: Double
    @State private var standardCm: Double
    @State private var attemptIndex: Int16
    @State private var selectedPole: Pole?
    @State private var showTechnicalDetails = false

    // UI state
    @State private var showingDeleteAlert = false
    @State private var showingDeleteMediaAlert = false
    @State private var mediaToDeleteId: UUID?

    // Measurement ranges
    @AppStorage("runStartMinCm") private var runStartMinCm: Double = 0
    @AppStorage("runStartMaxCm") private var runStartMaxCm: Double = 3048
    @AppStorage("handHoldMinCm") private var handHoldMinCm: Double = 213
    @AppStorage("handHoldMaxCm") private var handHoldMaxCm: Double = 487
    @AppStorage("takeOffStepMinCm") private var takeOffStepMinCm: Double = 0
    @AppStorage("takeOffStepMaxCm") private var takeOffStepMaxCm: Double = 1524
    @AppStorage("standardMinCm") private var standardMinCm: Double = 0
    @AppStorage("standardMaxCm") private var standardMaxCm: Double = 152

    init(jump: Jump, onSave: ((Jump) -> Void)? = nil) {
        self.jump = jump
        self.onSave = onSave

        // Initialize state from jump
        _barHeightCm = State(initialValue: jump.barHeightCm)
        _result = State(initialValue: jump.result ?? "miss")
        _comment = State(initialValue: jump.comment ?? "")
        _useBar = State(initialValue: jump.useBar)
        _runStartCm = State(initialValue: jump.runStartCm)
        _handHoldCm = State(initialValue: jump.handHoldCm)
        _takeOffStepCm = State(initialValue: jump.takeOffStepCm)
        _standardCm = State(initialValue: jump.standardCm)
        _attemptIndex = State(initialValue: jump.attemptIndex)
        _selectedPole = State(initialValue: jump.pole)
    }

    var body: some View {
        Form {
            ResultSection(result: $result, shouldDisablePass: shouldDisablePass)
            HeightAttemptEditSection(
                barHeightCm: $barHeightCm,
                attemptIndex: $attemptIndex,
                session: jump.session!,
                currentJump: jump
            )
            PoleSelectionSection(selectedPole: $selectedPole, athlete: jump.session?.athlete)
            TechnicalDetailsSection(
                showTechnicalDetails: $showTechnicalDetails,
                useBar: $useBar,
                runStartCm: $runStartCm,
                handHoldCm: $handHoldCm,
                takeOffStepCm: $takeOffStepCm,
                standardCm: $standardCm,
                sessionType: jump.session?.type,
                useMetric: useMetric
            )
            MediaSectionEdit(jump: jump)
            CommentSection(comment: $comment)
            JumpDeleteSection(showingDeleteAlert: $showingDeleteAlert)
        }
        .navigationTitle("Edit Jump")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .cancellationAction) {
                Button("Cancel") {
                    dismiss()
                }
            }

            ToolbarItem(placement: .primaryAction) {
                Button("Save") {
                    saveJump()
                }
                .fontWeight(.semibold)
            }
        }

        .alert("Delete Jump", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                deleteJump()
            }
        } message: {
            Text("Are you sure you want to delete this jump? This action cannot be undone.")
        }
        .alert("Delete Media", isPresented: $showingDeleteMediaAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                if let mediaId = mediaToDeleteId {
                    deleteMedia(mediaId)
                }
            }
        } message: {
            Text("Are you sure you want to delete this media item?")
        }
    }

    // MARK: - Form Sections

    private var resultSection: some View {
        Section(header: Text("Result")) {
            Picker("Result", selection: $result) {
                Text("Make").tag("make")
                Text("Miss").tag("miss")
                if !shouldDisablePass {
                    Text("Pass").tag("pass")
                }
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }

    private var heightSection: some View {
        Section(header: Text("Height & Attempt")) {
            HStack {
                Text("Height")
                Spacer()
                let heightFormat = HeightConverter.formatHeightTwoLine(cm: barHeightCm)
                VStack(alignment: .trailing) {
                    Text(heightFormat.primary)
                        .fontWeight(.bold)
                    if let secondary = heightFormat.secondary {
                        Text(secondary)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }

            HStack {
                Text("Attempt")
                Spacer()
                Text("#\(attemptIndex)")
                    .foregroundColor(.secondary)
            }
        }
    }



    private var technicalSection: some View {
        Section {
            DisclosureGroup("Technical Details", isExpanded: $showTechnicalDetails) {
                if jump.session?.type?.lowercased() == "practice" {
                    HStack {
                        Text("Bar/Bungee")
                        Spacer()
                        Picker("Bar/Bungee", selection: $useBar) {
                            Text("Bar").tag(true)
                            Text("Bungee").tag(false)
                        }
                        .pickerStyle(SegmentedPickerStyle())
                        .frame(width: 120)
                    }
                }

                MeasurementSlider(
                    title: "Run Start",
                    value: $runStartCm,
                    range: runStartMinCm...runStartMaxCm,
                    step: useMetric ? 1 : 2.54,
                    unit: useMetric ? "cm" : "ft"
                )

                MeasurementSlider(
                    title: "Hand Hold",
                    value: $handHoldCm,
                    range: handHoldMinCm...handHoldMaxCm,
                    step: useMetric ? 1 : 2.54,
                    unit: useMetric ? "cm" : "ft"
                )

                MeasurementSlider(
                    title: "Take-Off Step",
                    value: $takeOffStepCm,
                    range: takeOffStepMinCm...takeOffStepMaxCm,
                    step: useMetric ? 1 : 2.54,
                    unit: useMetric ? "cm" : "ft"
                )

                MeasurementSlider(
                    title: "Standard",
                    value: $standardCm,
                    range: standardMinCm...standardMaxCm,
                    step: useMetric ? 1 : 2.54,
                    unit: useMetric ? "cm" : "in"
                )
            }
        }
    }



    // MARK: - Helper Properties

    private var shouldDisablePass: Bool {
        guard jump.session?.type?.lowercased() != "practice" else { return false }

        // If this jump is already a pass, allow editing it
        if jump.result == "pass" {
            return false
        }

        let allJumps = jump.session?.jumps?.allObjects as? [Jump] ?? []
        let hasMakeAtThisHeight = allJumps.contains { otherJump in
            abs(otherJump.barHeightCm - jump.barHeightCm) < 0.1 &&
            otherJump.result == "make" &&
            otherJump != jump
        }

        return hasMakeAtThisHeight
    }

    // MARK: - Actions

    private func saveJump() {
        updateJump()
        onSave?(jump)
        dismiss()
    }



    private func updateJump() {
        jump.barHeightCm = barHeightCm
        jump.barHeightIn = barHeightCm / 2.54
        jump.result = result
        jump.comment = comment.isEmpty ? nil : comment
        jump.useBar = useBar
        jump.runStartCm = runStartCm
        jump.handHoldCm = handHoldCm
        jump.takeOffStepCm = takeOffStepCm
        jump.standardCm = standardCm
        jump.attemptIndex = attemptIndex
        jump.pole = selectedPole

        do {
            try viewContext.save()
        } catch {
            print("Error saving jump: \(error)")
        }
    }

    private func deleteJump() {
        viewContext.delete(jump)

        do {
            try viewContext.save()
            dismiss()
        } catch {
            print("Error deleting jump: \(error)")
        }
    }

    private func deleteMedia(_ mediaId: UUID) {
        let success = jump.removeMedia(withId: mediaId)

        if success {
            do {
                try viewContext.save()
                print("Successfully removed media from jump")
            } catch {
                print("Error removing media from jump: \(error)")
            }
        }
    }
}

// MARK: - Supporting Views
