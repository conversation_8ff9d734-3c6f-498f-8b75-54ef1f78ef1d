import SwiftUI

/// A reusable technical detail picker component that displays a single scrollable list of values
/// with both metric and imperial units displayed according to user preference
struct TechnicalDetailSelectorView: View {
    @Environment(\.dismiss) private var dismiss

    // Use the global AppTheme.useMetricSystem directly
    private var useMetric: Bool { AppTheme.useMetricSystem }

    // Binding to the selected value in cm
    @Binding var selectedValue: Double

    // Configuration for the selector
    let title: String
    let minCm: Double
    let maxCm: Double
    let step: Double
    let onSelect: (Double) -> Void
    let formatImperial: (Double) -> String

    // Generate the list of values based on the range and step
    private var valueOptions: [Double] {
        var values: [Double] = []
        let minValue = max(minCm, 0)
        let maxValue = min(maxCm, 10000) // Set a reasonable upper limit

        // If using imperial, use quarter-inch steps
        let stepSize = useMetric ? step : HeightConverter.inchesToCm(0.25)

        var currentValue = minValue
        while currentValue <= maxValue {
            values.append(currentValue)
            currentValue += stepSize
        }

        // Make sure the current selected value is in the list
        // This is important for proper scrolling and selection
        if !values.contains(where: { abs($0 - selectedValue) < 0.01 }) {
            // Add the selected value to the list and sort
            values.append(selectedValue)
            values.sort()
            print("Added selected value \(selectedValue) to options list")
        }

        return values
    }

    // Find the closest value to the selected value
    private func findClosestValue(to value: Double, in values: [Double]) -> Double {
        guard !values.isEmpty else { return value }

        // First check for exact match
        if let exactMatch = values.first(where: { abs($0 - value) < 0.01 }) {
            return exactMatch
        }

        // Otherwise find closest
        return values.min(by: { abs($0 - value) < abs($1 - value) }) ?? value
    }

    // Store initial value to use for cancel
    @State private var initialValue: Double

    // Initialize with the initial value
    init(selectedValue: Binding<Double>, title: String, minCm: Double, maxCm: Double, step: Double, onSelect: @escaping (Double) -> Void, formatImperial: @escaping (Double) -> String) {
        self._selectedValue = selectedValue
        self.title = title
        self.minCm = minCm
        self.maxCm = maxCm
        self.step = step
        self.onSelect = onSelect
        self.formatImperial = formatImperial
        self._initialValue = State(initialValue: selectedValue.wrappedValue)
    }

    var body: some View {
        NavigationStack {
            // Disable interactive dismissal to prevent swipe-down dismissal
            // This ensures we always call onSelect with a value
            VStack {
                Text(title)
                    .font(.headline)
                    .padding(.top)

                ScrollViewReader { scrollProxy in
                    List {
                        ForEach(valueOptions, id: \.self) { value in
                            Button(action: {
                                selectedValue = value
                                onSelect(value)
                                dismiss()
                            }) {
                                HStack {
                                    if useMetric {
                                        Text(HeightConverter.cmToMetersString(value))
                                            .fontWeight(.bold)
                                        Text("•")
                                        Text(formatImperial(value))
                                            .foregroundColor(.secondary)
                                    } else {
                                        Text(formatImperial(value))
                                            .fontWeight(.bold)
                                        Text("•")
                                        Text(HeightConverter.cmToMetersString(value))
                                            .foregroundColor(.secondary)
                                    }

                                    Spacer()

                                    // Show a checkmark for the currently selected value
                                    if abs(value - selectedValue) < 0.01 {
                                        Image(systemName: "checkmark")
                                            .foregroundColor(.accentColor)
                                    }
                                }
                            }
                            .id(value) // Use the value as the ID for scrolling
                        }
                    }
                    .onAppear {
                        // Find the closest value to the selected value
                        let closestValue = findClosestValue(to: selectedValue, in: valueOptions)

                        // Log for debugging
                        print("Scrolling to value: \(closestValue) (selected: \(selectedValue), options count: \(valueOptions.count))")

                        // Scroll to the selected value with animation after a short delay
                        // Use a longer delay to ensure the view is fully loaded
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                            withAnimation {
                                scrollProxy.scrollTo(closestValue, anchor: .center)
                            }
                        }
                    }
                    .task {
                        // This is a backup to ensure scrolling happens even if onAppear doesn't trigger properly
                        try? await Task.sleep(nanoseconds: 600_000_000) // 0.6 seconds
                        let closestValue = findClosestValue(to: selectedValue, in: valueOptions)
                        print("Task backup scrolling to: \(closestValue)")
                    }
                }

                Button("Cancel") {
                    // Restore the original value
                    selectedValue = initialValue
                    onSelect(initialValue)
                    dismiss()
                }
                .padding()
                .buttonStyle(.bordered)
            }
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        onSelect(selectedValue)
                        dismiss()
                    }
                    .foregroundColor(.accentColor)
                }
            }
            .onDisappear {
                // Make sure we call onSelect with the initial value if the sheet is dismissed by tapping outside
                // This ensures we don't leave the view in an inconsistent state
                print("TechnicalDetailSelectorView disappeared")
            }
            .interactiveDismissDisabled(true) // Prevent swipe-down dismissal
        }
    }
}

// Helper formatters for different technical details
extension TechnicalDetailSelectorView {
    // Format for Run Start and Take-Off Step (feet and inches)
    static func formatFeetInches(_ cm: Double) -> String {
        return HeightConverter.cmToFeetInchesString(cm)
    }

    // Format for Hand Hold (feet and inches)
    static func formatHandHold(_ cm: Double) -> String {
        return HeightConverter.cmToFeetInchesString(cm)
    }

    // Format for Standard (inches only)
    static func formatStandard(_ cm: Double) -> String {
        let inches = cm / 2.54

        // Round to the nearest quarter inch
        let quarterInches = Int(round(inches * 4))
        let wholeInches = quarterInches / 4
        let fractionNumerator = quarterInches % 4

        let inchesFormatted: String

        if fractionNumerator == 0 {
            // Whole inch
            inchesFormatted = "\(wholeInches)"
        } else if fractionNumerator == 1 {
            // Quarter inch
            inchesFormatted = "\(wholeInches) 1/4"
        } else if fractionNumerator == 2 {
            // Half inch
            inchesFormatted = "\(wholeInches) 1/2"
        } else {
            // Three-quarter inch
            inchesFormatted = "\(wholeInches) 3/4"
        }

        return "\(inchesFormatted)\""
    }
}

#Preview {
    TechnicalDetailSelectorView(
        selectedValue: .constant(300),
        title: "Run Start",
        minCm: 0,
        maxCm: 3048,
        step: 5,
        onSelect: { _ in },
        formatImperial: TechnicalDetailSelectorView.formatFeetInches
    )
}
