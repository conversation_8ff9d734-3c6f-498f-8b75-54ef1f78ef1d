import SwiftUI
import UIKit

struct PoleEditView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss

    // Pole properties
    @State private var name: String = ""
    @State private var brand: String = ""
    @State private var weight: Double = 160
    @State private var lengthFeet: Int = 14
    @State private var lengthInches: Int = 0
    @State private var color: String = ""
    @State private var selectedColor: Color = .blue

    // UI state
    @State private var showingLengthPicker = false
    @State private var showingWeightPicker = false

    private let athlete: Athlete
    private let pole: Pole?
    private let isEditing: Bool

    // Constants for pickers
    private let minWeight: Double = 80
    private let maxWeight: Double = 250
    private let weightStep: Double = 5

    private let minLengthFeet: Int = 8
    private let maxLengthFeet: Int = 17

    init(athlete: Athlete, pole: Pole? = nil) {
        self.athlete = athlete
        self.pole = pole
        self.isEditing = pole != nil

        if let pole = pole {
            // Initialize state with existing pole values
            _name = State(initialValue: pole.name ?? "")
            _brand = State(initialValue: pole.brand ?? "")
            _weight = State(initialValue: pole.weight)
            _color = State(initialValue: pole.color ?? "")

            // Convert color string to Color if possible
            if let colorString = pole.color, !colorString.isEmpty {
                if let uiColor = UIColor(named: colorString) {
                    _selectedColor = State(initialValue: Color(uiColor))
                } else {
                    // Try to parse hex color
                    _selectedColor = State(initialValue: Color(hex: colorString))
                }
            }

            // Convert length from cm to feet/inches
            let inches = pole.lengthIn
            _lengthFeet = State(initialValue: Int(inches / 12))
            _lengthInches = State(initialValue: Int(inches.truncatingRemainder(dividingBy: 12)))
        } else {
            // For new poles, set a default name based on the selected color
            if name.isEmpty {
                // Get a user-friendly color name for the default blue color
                _name = State(initialValue: "Blue Pole")
            }
        }
    }

    var body: some View {
        NavigationStack {
            Form {
                Section(header: Text("Pole Details")) {
                    // Custom TextField for Name with color preview
                    HStack {
                        Text("Name")
                            .foregroundColor(.secondary)
                            .frame(width: 60, alignment: .leading)

                        TextField("", text: $name)
                            .font(.headline.bold())
                            .foregroundColor(color.isEmpty ? .primary : Color(hex: color))
                            .padding(8)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                    }

                    // Custom TextField for Brand
                    HStack {
                        Text("Brand")
                            .foregroundColor(.secondary)
                            .frame(width: 60, alignment: .leading)

                        TextField("", text: $brand)
                            .padding(8)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                    }

                    // Length picker
                    Button(action: {
                        showingLengthPicker = true
                    }) {
                        HStack {
                            Text("Length")
                            Spacer()
                            VStack(alignment: .trailing) {
                                Text("\(lengthFeet)' \(lengthInches)\"")
                                    .foregroundColor(.primary)

                                // Calculate and display metric equivalent
                                let totalInches = Double(lengthFeet * 12 + lengthInches)
                                let lengthCm = totalInches * 2.54
                                Text(HeightConverter.cmToMetersString(lengthCm))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .sheet(isPresented: $showingLengthPicker) {
                        LengthPickerView(
                            feet: $lengthFeet,
                            inches: $lengthInches,
                            minFeet: minLengthFeet,
                            maxFeet: maxLengthFeet
                        )
                    }

                    // Weight picker
                    Button(action: {
                        showingWeightPicker = true
                    }) {
                        HStack {
                            Text("Weight")
                            Spacer()
                            VStack(alignment: .trailing) {
                                Text("\(Int(weight)) lbs")
                                    .foregroundColor(.primary)

                                // Calculate and display metric equivalent
                                let kg = weight * 0.453592
                                Text(String(format: "%.1f kg", kg))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .sheet(isPresented: $showingWeightPicker) {
                        WeightPickerView(
                            weight: $weight,
                            minWeight: minWeight,
                            maxWeight: maxWeight,
                            step: weightStep
                        )
                    }

                    // Color picker with enhanced visibility
                    HStack {
                        Text("Color")
                            .foregroundColor(.secondary)
                            .frame(width: 60, alignment: .leading)

                        HStack {
                            ColorPicker("", selection: $selectedColor)
                                .labelsHidden()

                            // Preview of the selected color
                            RoundedRectangle(cornerRadius: 6)
                                .fill(selectedColor)
                                .frame(width: 30, height: 30)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 6)
                                        .stroke(Color.gray, lineWidth: 1)
                                )

                            // Show the color name
                            Text(getColorName(from: selectedColor))
                                .foregroundColor(selectedColor)
                                .padding(.leading, 4)
                                .font(.subheadline)
                        }
                        .padding(8)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                    }
                    .onChange(of: selectedColor) { oldValue, newValue in
                        // Convert the Color to a hex string and store it
                        color = selectedColor.toHex()

                        // Force UI refresh for the name color
                        let tempName = name
                        name = ""
                        name = tempName

                        // Update the name if it's empty or if it matches a color name
                        if name.isEmpty || isNameMatchingColor(name) {
                            // Get a user-friendly color name
                            let colorName = getColorName(from: selectedColor)
                            name = "\(colorName) Pole"
                        }
                    }
                }

                Section {
                    Button(action: savePole) {
                        Text(isEditing ? "Save Changes" : "Add Pole")
                            .frame(maxWidth: .infinity, alignment: .center)
                            .foregroundColor(AppTheme.accentColor)
                    }
                    .disabled(name.isEmpty)
                }
            }
            .navigationTitle(isEditing ? "Edit Pole" : "Add Pole")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }

    // Helper function to check if the name matches a color name
    private func isNameMatchingColor(_ name: String) -> Bool {
        // Common color names that might be in pole names
        let colorNames = ["Red", "Blue", "Green", "Yellow", "Orange", "Purple", "Black", "White", "Gray", "Pink"]

        // Check if the name starts with any color name
        for colorName in colorNames {
            if name.lowercased().contains(colorName.lowercased()) {
                return true
            }
        }

        // Check if the name ends with "Pole" which suggests it was auto-generated
        if name.hasSuffix("Pole") {
            return true
        }

        return false
    }

    // Helper function to get a user-friendly color name from a Color
    private func getColorName(from color: Color) -> String {
        // Convert to RGB components
        let uiColor = UIColor(color)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0

        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)

        // Simple color detection based on RGB values
        if red > 0.7 && green < 0.3 && blue < 0.3 {
            return "Red"
        } else if red < 0.3 && green > 0.6 && blue < 0.3 {
            return "Green"
        } else if red < 0.3 && green < 0.3 && blue > 0.7 {
            return "Blue"
        } else if red > 0.7 && green > 0.7 && blue < 0.3 {
            return "Yellow"
        } else if red > 0.7 && green > 0.4 && green < 0.7 && blue < 0.3 {
            return "Orange"
        } else if red > 0.5 && green < 0.3 && blue > 0.5 {
            return "Purple"
        } else if red < 0.2 && green < 0.2 && blue < 0.2 {
            return "Black"
        } else if red > 0.9 && green > 0.9 && blue > 0.9 {
            return "White"
        } else if red > 0.4 && red < 0.6 && green > 0.4 && green < 0.6 && blue > 0.4 && blue < 0.6 {
            return "Gray"
        } else if red > 0.7 && green < 0.5 && blue > 0.5 {
            return "Pink"
        }

        // Default to a generic name based on the hex value
        return "Custom"
    }

    private func savePole() {
        withAnimation {
            // Convert feet/inches to cm
            let totalInches = Double(lengthFeet * 12 + lengthInches)
            let lengthCm = totalInches * 2.54

            if let existingPole = pole {
                // Update existing pole
                existingPole.name = name
                existingPole.brand = brand
                existingPole.weight = weight
                existingPole.lengthCm = lengthCm
                existingPole.lengthIn = totalInches
                existingPole.color = color
            } else {
                // Create new pole
                // Get the next order number
                let order = (athlete.poles?.count ?? 0)

                _ = Pole.create(
                    in: viewContext,
                    athlete: athlete,
                    name: name,
                    brand: brand.isEmpty ? nil : brand,
                    weight: weight,
                    lengthCm: lengthCm,
                    color: color.isEmpty ? nil : color,
                    order: Int16(order)
                )
            }

            // Save the context
            do {
                try viewContext.save()
                dismiss()
            } catch {
                let nsError = error as NSError
                print("Error saving pole: \(nsError), \(nsError.userInfo)")
            }
        }
    }
}

// Helper view for picking pole length
struct LengthPickerView: View {
    @Environment(\.dismiss) private var dismiss
    @AppStorage("useMetricSystem") private var useMetricSystem = false

    @Binding var feet: Int
    @Binding var inches: Int

    let minFeet: Int
    let maxFeet: Int

    // Create a combined value for the picker
    @State private var combinedValue: Int

    // Initialize with the combined value
    init(feet: Binding<Int>, inches: Binding<Int>, minFeet: Int, maxFeet: Int) {
        self._feet = feet
        self._inches = inches

        // Ensure min and max feet are valid
        self.minFeet = max(0, minFeet)
        self.maxFeet = max(self.minFeet, maxFeet)

        // Initialize the combined value with validation
        let feetValue = max(0, feet.wrappedValue)
        let inchesValue = max(0, min(11, inches.wrappedValue))

        // Ensure the combined value is within the valid range
        let minTotalInches = self.minFeet * 12
        let maxTotalInches = self.maxFeet * 12 + 11
        let initialTotalInches = feetValue * 12 + inchesValue

        // If outside valid range, use default value
        let validatedTotalInches = max(minTotalInches, min(maxTotalInches, initialTotalInches))

        self._combinedValue = State(initialValue: validatedTotalInches)
    }

    // Generate all possible length options
    private var lengthOptions: [Int] {
        var options: [Int] = []

        // Ensure min and max feet are valid
        guard minFeet >= 0, maxFeet >= minFeet else {
            // Default values if parameters are invalid (8' to 17')
            return (8...17).flatMap { foot in
                (0..<12).map { inch in
                    foot * 12 + inch
                }
            }
        }

        for foot in minFeet...maxFeet {
            for inch in 0..<12 {
                options.append(foot * 12 + inch)
            }
        }
        return options
    }

    // Format the combined value for display
    private func formattedValue(_ totalInches: Int) -> String {
        // Ensure totalInches is valid
        guard totalInches >= 0 else {
            return "Invalid length"
        }

        let feet = totalInches / 12
        let inches = totalInches % 12
        let cm = Double(totalInches) * 2.54
        let imperial = "\(feet)' \(inches)\""
        let metric = HeightConverter.cmToMetersString(cm)
        return "\(imperial) • \(metric)"
    }

    var body: some View {
        NavigationStack {
            VStack {
                Picker("Length", selection: $combinedValue) {
                    ForEach(lengthOptions, id: \.self) { totalInches in
                        Text(formattedValue(totalInches))
                            .tag(totalInches)
                    }
                }
                .pickerStyle(.wheel)
                .padding()
                .onChange(of: combinedValue) { _, newValue in
                    // Update the feet and inches bindings
                    feet = newValue / 12
                    inches = newValue % 12
                }
            }
            .navigationTitle("Select Length")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .confirmationAction) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// Helper view for picking pole weight
struct WeightPickerView: View {
    @Environment(\.dismiss) private var dismiss
    @AppStorage("useMetricSystem") private var useMetricSystem = false

    @Binding var weight: Double

    let minWeight: Double
    let maxWeight: Double
    let step: Double

    // Create a computed property for the weight values
    private var weightValues: [Double] {
        var values: [Double] = []

        // Ensure min, max, and step are valid
        guard minWeight.isFinite && maxWeight.isFinite && step.isFinite,
              minWeight >= 0, maxWeight >= minWeight, step > 0 else {
            return [80, 100, 120, 140, 160, 180, 200, 220, 240] // Default values if parameters are invalid
        }

        var currentValue = minWeight
        while currentValue <= maxWeight {
            values.append(currentValue)
            currentValue += step
        }
        return values
    }

    // Convert pounds to kilograms
    private func lbsToKg(_ lbs: Double) -> Double {
        // Ensure lbs is valid
        guard lbs.isFinite && lbs >= 0 else {
            return 0
        }
        return lbs * 0.453592
    }

    // Format weight with both units
    private func formattedWeight(_ lbs: Double) -> String {
        // Ensure lbs is valid
        guard lbs.isFinite && lbs >= 0 else {
            return "Invalid weight"
        }

        let kg = lbsToKg(lbs)
        return "\(Int(lbs)) lbs • \(String(format: "%.1f", kg)) kg"
    }

    var body: some View {
        NavigationStack {
            VStack {
                Picker("Weight", selection: $weight) {
                    ForEach(weightValues, id: \.self) { value in
                        Text(formattedWeight(value))
                            .tag(value)
                    }
                }
                .pickerStyle(.wheel)
                .padding()
            }
            .navigationTitle("Select Weight")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .confirmationAction) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    let context = PersistenceController.preview.container.viewContext
    let athlete = Athlete.create(in: context, name: "Test Athlete")

    PoleEditView(athlete: athlete)
        .environment(\.managedObjectContext, context)
}
