import SwiftUI
import CoreData
import PhotosUI

struct JumpViewNew: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss

    // Use the global AppTheme.useMetricSystem directly
    private var useMetric: Bool { AppTheme.useMetricSystem }

    // Parameters passed from navigation
    let session: Session
    let barHeightCm: Double
    let attemptIndex: Int16
    let onSave: ((Jump) -> Void)?

    // Form state
    @State private var result: String = "miss"
    @State private var comment: String = ""
    @State private var useBar: Bool = true
    @State private var runStartCm: Double = 1000
    @State private var handHoldCm: Double = 350
    @State private var takeOffStepCm: Double = 300
    @State private var standardCm: Double = 45
    @State private var selectedPole: Pole?
    @State private var showTechnicalDetails = false

    // Media handling
    @State private var selectedMediaIds: [String] = []
    @State private var mediaStructs: [JumpMediaStruct] = []

    // Measurement ranges
    @AppStorage("runStartMinCm") private var runStartMinCm: Double = 0
    @AppStorage("runStartMaxCm") private var runStartMaxCm: Double = 3048
    @AppStorage("handHoldMinCm") private var handHoldMinCm: Double = 213
    @AppStorage("handHoldMaxCm") private var handHoldMaxCm: Double = 487
    @AppStorage("takeOffStepMinCm") private var takeOffStepMinCm: Double = 0
    @AppStorage("takeOffStepMaxCm") private var takeOffStepMaxCm: Double = 1524
    @AppStorage("standardMinCm") private var standardMinCm: Double = 0
    @AppStorage("standardMaxCm") private var standardMaxCm: Double = 152

    init(session: Session, barHeightCm: Double, attemptIndex: Int16, onSave: ((Jump) -> Void)? = nil) {
        self.session = session
        self.barHeightCm = barHeightCm
        self.attemptIndex = attemptIndex
        self.onSave = onSave

        // Initialize with previous jump values if available
        let jumps = session.jumps?.allObjects as? [Jump] ?? []
        let sortedJumps = jumps.sorted { $0.order > $1.order }

        if let lastJump = sortedJumps.first {
            _runStartCm = State(initialValue: lastJump.runStartCm)
            _handHoldCm = State(initialValue: lastJump.handHoldCm)
            _takeOffStepCm = State(initialValue: lastJump.takeOffStepCm)
            _standardCm = State(initialValue: lastJump.standardCm)
            _selectedPole = State(initialValue: lastJump.pole)
            _useBar = State(initialValue: lastJump.useBar)
        }
    }

    var body: some View {
        Form {
            ResultSection(result: $result, shouldDisablePass: shouldDisablePass)
            HeightAttemptSection(barHeightCm: barHeightCm, attemptIndex: attemptIndex)
            PoleSelectionSection(selectedPole: $selectedPole, athlete: session.athlete)
            TechnicalDetailsSection(
                showTechnicalDetails: $showTechnicalDetails,
                useBar: $useBar,
                runStartCm: $runStartCm,
                handHoldCm: $handHoldCm,
                takeOffStepCm: $takeOffStepCm,
                standardCm: $standardCm,
                sessionType: session.type,
                useMetric: useMetric
            )
            MediaSectionNew(
                selectedMediaIds: $selectedMediaIds,
                mediaStructs: $mediaStructs
            )
            CommentSection(comment: $comment)
        }
        .navigationTitle("New Jump")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .cancellationAction) {
                Button("Cancel") {
                    dismiss()
                }
            }

            ToolbarItem(placement: .primaryAction) {
                Button("Save") {
                    saveJump()
                }
                .fontWeight(.semibold)
            }
        }

    }

    // MARK: - Helper Properties

    private var shouldDisablePass: Bool {
        guard session.type?.lowercased() != "practice" else { return false }

        let allJumps = session.jumps?.allObjects as? [Jump] ?? []
        let hasMakeAtThisHeight = allJumps.contains { jump in
            abs(jump.barHeightCm - barHeightCm) < 0.1 && jump.result == "make"
        }

        return hasMakeAtThisHeight
    }

    // MARK: - Actions

    private func saveJump() {
        let jump = createJump()
        onSave?(jump)
        dismiss()
    }



    private func createJump() -> Jump {
        let order = (session.jumps?.count ?? 0) + 1

        let newJump = Jump.create(
            in: viewContext,
            session: session,
            order: Int16(order),
            heightCm: barHeightCm,
            result: result,
            comment: comment.isEmpty ? nil : comment,
            attemptIndex: attemptIndex,
            runStartCm: runStartCm,
            handHoldCm: handHoldCm,
            takeOffStepCm: takeOffStepCm,
            standardCm: standardCm,
            useBar: useBar,
            pole: selectedPole
        )

        // Add selected media to the jump
        for mediaStruct in mediaStructs {
            _ = newJump.addMediaStruct(mediaStruct)
        }

        do {
            try viewContext.save()
        } catch {
            print("Error saving jump: \(error)")
        }

        return newJump
    }
}

// MARK: - Supporting Views
