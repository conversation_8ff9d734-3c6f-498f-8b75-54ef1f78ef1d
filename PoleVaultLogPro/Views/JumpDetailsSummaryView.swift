import SwiftUI

struct JumpDetailsSummaryView: View {
    @Environment(\.managedObjectContext) private var viewContext

    // Use the global AppTheme.useMetricSystem directly
    private var useMetric: Bool { AppTheme.useMetricSystem }

    let session: Session

    // State to force refresh when needed
    @State private var refreshID = UUID()

    // Fetch jumps for this session
    @FetchRequest private var jumps: FetchedResults<Jump>

    init(session: Session) {
        self.session = session

        // Configure the fetch request for jumps
        // Fetch all jumps for this session
        _jumps = FetchRequest<Jump>(
            sortDescriptors: [
                NSSortDescriptor(keyPath: \Jump.order, ascending: false) // Most recent first
            ],
            predicate: NSPredicate(format: "session == %@", session)
        )
    }

    var body: some View {
        VStack(spacing: 0) {
            // Header
            HStack {
                Text("Recent Jumps")
                    .font(.headline)
                    .padding(.leading)

                Spacer()
            }
            .padding(.vertical, 8)
            .background(Color(.systemBackground))
            .overlay(
                Rectangle()
                    .frame(height: 1)
                    .foregroundColor(Color.gray.opacity(0.3)),
                alignment: .bottom
            )

            // Jump details list
            if jumps.isEmpty {
                Text("No jumps recorded yet")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                ScrollView {
                    LazyVStack(spacing: 0) {
                        ForEach(jumps) { jump in
                            JumpSummaryRow(jump: jump)
                                .padding(.vertical, 8)
                                .padding(.horizontal)
                                .background(Color(.systemBackground))
                                .overlay(
                                    Rectangle()
                                        .frame(height: 1)
                                        .foregroundColor(Color.gray.opacity(0.2)),
                                    alignment: .bottom
                                )
                                .contentShape(Rectangle()) // Make the entire row tappable
                        }
                    }
                }
            }
        }
        .background(Color(.systemGroupedBackground))
        .id(refreshID) // Force refresh when refreshID changes
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("SessionDataChanged"))) { notification in
            // Only refresh if the notification is for this session
            if let notifiedSession = notification.object as? Session, notifiedSession.id == session.id {
                print("Refreshing Recent Jumps view for session \(session.id ?? "unknown")")
                refreshID = UUID() // Force a refresh
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("MediaFilesChanged"))) { _ in
            // Refresh the view when media files are changed
            print("Refreshing Recent Jumps view due to media files change")
            refreshID = UUID() // Force a refresh
        }
    }
}

struct JumpSummaryRow: View {
    @Environment(\.managedObjectContext) private var viewContext

    // Use the global AppTheme settings directly
    private var useMetric: Bool { AppTheme.useMetricSystem }
    private var showSecondaryUnits: Bool { AppTheme.showSecondaryUnits }
    let jump: Jump

    // State for showing the jump edit sheet
    @State private var editJumpContext: EditJumpContext?

    // State to force refresh when media files change
    @State private var refreshID = UUID()

    var body: some View {
        VStack(spacing: 0) {
            // Main row content
            HStack {
                // Main jump details button
                Button(action: {
                    editJumpContext = EditJumpContext(jump: jump)
                }) {
                    VStack(alignment: .leading, spacing: 4) {
                        // Top row with height and result
                        HStack {
                            // Height with playback button
                            HStack(spacing: 12) {
                                // Height
                                VStack(alignment: .leading, spacing: 1) {
                                    // Primary unit (metric or imperial based on preference)
                                    if useMetric {
                                        Text("\(Int(jump.barHeightCm)) cm")
                                            .font(.headline)
                                            .fontWeight(.bold)
                                            .foregroundColor(.primary)
                                    } else {
                                        Text(HeightConverter.cmToFeetInchesString(jump.barHeightCm))
                                            .font(.headline)
                                            .fontWeight(.bold)
                                            .foregroundColor(.primary)
                                    }

                                    // Secondary unit (only if enabled)
                                    if showSecondaryUnits {
                                        if useMetric {
                                            Text(HeightConverter.cmToFeetInchesString(jump.barHeightCm))
                                                .font(.subheadline)
                                                .foregroundColor(.secondary)
                                        } else {
                                            Text("\(Int(jump.barHeightCm)) cm")
                                                .font(.subheadline)
                                                .foregroundColor(.secondary)
                                        }
                                    }
                                }

                                // Media playback button (only if media exists)
                                if hasMedia {
                                    NavigationLink(destination: MediaBrowserView(jump: jump)) {
                                        Image(systemName: hasVideo ? "play.circle.fill" : "photo.circle.fill")
                                            .font(.system(size: 26))
                                            .foregroundColor(.blue)
                                            .padding(.leading, 4)
                                    }
                                    .buttonStyle(BorderlessButtonStyle())
                                }

                                Spacer()
                            }

                            Spacer()

                            // Media indicator (now on the left of bar/bungee)
                            if hasMedia {
                                Image(systemName: hasVideo ? "video.fill" : "photo.fill")
                                    .font(.caption)
                                    .foregroundColor(.blue)
                                    .padding(.trailing, 4)
                            }

                            // Bar/Bungee indicator for practice sessions
                            if let session = jump.session, session.type?.lowercased() == "practice" {
                                Image(systemName: jump.useBar ? "minus" : "water.waves")
                                    .font(.caption)
                                    .foregroundColor(jump.useBar ? .primary : .blue)
                                    .padding(.trailing, 4)
                            }

                            // Pole name (if available)
                            if let pole = jump.pole, let poleName = pole.name {
                                if let colorHex = pole.color, !colorHex.isEmpty {
                                    Text(poleName)
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .foregroundColor(Color(hex: colorHex))
                                        .padding(.trailing, 4)
                                } else {
                                    Text(poleName)
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .padding(.trailing, 4)
                                }
                            }

                            // Result indicator
                            resultBadge
                        }

                    // Technical details row
                    HStack {
                        // Run Start
                        VStack(alignment: .leading) {
                            Text("Run")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            formatMeasurement(jump.runStartCm)
                        }

                        Spacer()

                        // Hand Hold
                        VStack(alignment: .leading) {
                            Text("Hold")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            formatMeasurement(jump.handHoldCm)
                        }

                        Spacer()

                        // Take Off Step
                        VStack(alignment: .leading) {
                            Text("Step")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            formatMeasurement(jump.takeOffStepCm)
                        }

                        Spacer()

                        // Standard
                        VStack(alignment: .leading) {
                            Text("Standard")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            formatMeasurement(jump.standardCm)
                        }
                    }

                    // Comment
                    if let comment = jump.comment, !comment.isEmpty {
                        Text(comment)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                            .padding(.top, 2)
                    }
                }
                .foregroundColor(.primary) // Keep text in original color
            }
            .buttonStyle(BorderlessButtonStyle())
            .frame(maxWidth: .infinity)
        }
        }
        .sheet(item: $editJumpContext) { context in
            NavigationStack {
                JumpViewEdit(
                    jump: context.jump,
                    onSave: { updatedJump in
                        editJumpContext = nil
                        try? viewContext.save()
                    }
                )
            }
        }
        .id(refreshID) // Force refresh when refreshID changes
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("MediaFilesChanged"))) { _ in
            // Refresh the view when media files are changed
            print("Refreshing JumpSummaryRow due to media files change")
            refreshID = UUID() // Force a refresh
        }
    }

    // Check if the jump has any media attached
    private var hasMedia: Bool {
        // Check for media items
        let mediaItems = jump.getMediaItems()
        return !mediaItems.isEmpty
    }

    // Check if the jump has a video attached
    private var hasVideo: Bool {
        // Check for video media items
        let mediaItems = jump.getMediaItems()
        return mediaItems.contains(where: { $0.type == .video })
    }

    private var resultBadge: some View {
        let result = jump.result ?? "miss"
        let color: Color = result == "make" ? .green : (result == "miss" ? .red : .gray)
        let text = result == "make" ? "Make" : (result == "miss" ? "Miss" : "Pass")
        let symbol = result == "make" ? "checkmark.circle.fill" : (result == "miss" ? "xmark.circle.fill" : "arrow.right.circle.fill")

        return HStack(spacing: 4) {
            Image(systemName: symbol)
            Text(text)
        }
        .font(.caption)
        .foregroundColor(.white)
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(color)
        .cornerRadius(12)
    }

    private func formatMeasurement(_ value: Double) -> some View {
        if value == 0 {
            return AnyView(Text("-"))
        }

        // Format metric value
        let metricText = HeightConverter.cmToMetersString(value)

        // Format imperial value based on the measurement type
        let imperialText: String

        // Determine which formatter to use based on the measurement type
        if abs(value - jump.runStartCm) < 0.1 {
            // Run Start - use whole feet and inches
            imperialText = HeightConverter.formatStepMeasurement(cm: value)
        } else if abs(value - jump.handHoldCm) < 0.1 {
            // Hand Hold - use whole feet and inches
            imperialText = HeightConverter.formatHandHoldMeasurement(cm: value)
        } else if abs(value - jump.takeOffStepCm) < 0.1 {
            // Take-Off Step - use whole feet and inches
            imperialText = HeightConverter.formatStepMeasurement(cm: value)
        } else {
            // Standard or other - use quarter-inch precision
            imperialText = HeightConverter.cmToFeetInchesString(value)
        }

        // Return a view with primary value and optional secondary value
        return AnyView(
            VStack(alignment: .leading, spacing: 1) {
                // Primary unit (metric or imperial based on preference)
                if useMetric {
                    Text(metricText)
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                } else {
                    Text(imperialText)
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                }

                // Secondary unit (only if enabled)
                if showSecondaryUnits {
                    if useMetric {
                        Text(imperialText)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    } else {
                        Text(metricText)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
        )
    }
}

#Preview {
    let context = PersistenceController(inMemory: true).container.viewContext
    let session = Session.create(in: context, athlete: Athlete.create(in: context, name: "Test Athlete"), type: "practice")

    JumpDetailsSummaryView(session: session)
        .environment(\.managedObjectContext, context)
}
