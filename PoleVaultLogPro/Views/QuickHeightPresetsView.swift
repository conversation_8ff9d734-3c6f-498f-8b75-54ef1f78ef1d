import SwiftUI

struct QuickHeightPresetsView: View {
    @Environment(\.dismiss) private var dismiss
    
    // Presets stored as arrays of heights in cm
    @AppStorage("heightPresets") private var heightPresetsData: Data = Data()
    @State private var heightPresets: [Double] = []
    
    // For editing
    @State private var newPresetHeight: Double = 200
    @State private var isAddingPreset: Bool = false
    
    var body: some View {
        NavigationStack {
            List {
                Section(header: Text("Current Presets")) {
                    if heightPresets.isEmpty {
                        Text("No presets defined")
                            .foregroundColor(.secondary)
                            .italic()
                    } else {
                        ForEach(heightPresets.indices, id: \.self) { index in
                            HStack {
                                if AppTheme.useMetricSystem {
                                    Text("\(Int(heightPresets[index])) cm")
                                        .fontWeight(.bold)
                                    Text("(\(HeightConverter.cmToFeetInchesString(heightPresets[index])))")
                                        .foregroundColor(.secondary)
                                        .font(.footnote)
                                } else {
                                    Text(HeightConverter.cmToFeetInchesString(heightPresets[index]))
                                        .fontWeight(.bold)
                                    Text("(\(Int(heightPresets[index])) cm)")
                                        .foregroundColor(.secondary)
                                        .font(.footnote)
                                }
                                
                                Spacer()
                            }
                            .contentShape(Rectangle())
                            .swipeActions {
                                Button(role: .destructive) {
                                    withAnimation {
                                        heightPresets.remove(at: index)
                                        savePresets()
                                    }
                                } label: {
                                    Label("Delete", systemImage: "trash")
                                }
                            }
                        }
                        .onMove { indices, newOffset in
                            heightPresets.move(fromOffsets: indices, toOffset: newOffset)
                            savePresets()
                        }
                    }
                }
                
                Section {
                    Button("Add New Preset") {
                        isAddingPreset = true
                    }
                }
            }
            .navigationTitle("Height Presets")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    EditButton()
                }
                
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .sheet(isPresented: $isAddingPreset) {
                addPresetView
            }
            .onAppear {
                loadPresets()
            }
        }
    }
    
    private var addPresetView: some View {
        NavigationStack {
            Form {
                Section(header: Text("New Height Preset")) {
                    HStack {
                        Text("Height")
                        Spacer()
                        
                        if AppTheme.useMetricSystem {
                            Text("\(Int(newPresetHeight)) cm")
                                .fontWeight(.bold)
                            Text("(\(HeightConverter.cmToFeetInchesString(newPresetHeight)))")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        } else {
                            Text(HeightConverter.cmToFeetInchesString(newPresetHeight))
                                .fontWeight(.bold)
                            Text("(\(Int(newPresetHeight)) cm)")
                                .foregroundColor(.secondary)
                                .font(.footnote)
                        }
                    }
                    
                    Slider(value: $newPresetHeight, in: 91...609, step: 1)
                }
            }
            .navigationTitle("Add Height Preset")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        isAddingPreset = false
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Add") {
                        withAnimation {
                            heightPresets.append(newPresetHeight)
                            heightPresets.sort()
                            savePresets()
                            isAddingPreset = false
                        }
                    }
                }
            }
        }
    }
    
    private func loadPresets() {
        if let presets = try? JSONDecoder().decode([Double].self, from: heightPresetsData) {
            heightPresets = presets
        } else {
            // Default presets if none exist
            heightPresets = [152, 183, 213, 244, 274, 305, 335, 366, 396, 427, 457, 488]
            savePresets()
        }
    }
    
    private func savePresets() {
        if let encoded = try? JSONEncoder().encode(heightPresets) {
            heightPresetsData = encoded
        }
    }
}

#Preview {
    QuickHeightPresetsView()
}
