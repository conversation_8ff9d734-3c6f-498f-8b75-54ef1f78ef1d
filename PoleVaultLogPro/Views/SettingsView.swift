import SwiftUI
import CoreData
import UIKit
import CloudKit
import MessageUI

struct SettingsView: View {
    @Environment(\.managedObjectContext) private var viewContext

    // Settings
    @AppStorage("useMetricSystem") private var useMetricSystem = false
    @AppStorage("showSecondaryUnits") private var showSecondaryUnits = true
    @AppStorage("defaultSessionType") private var defaultSessionType = "Practice"
    @AppStorage("videoCacheWarningGB") private var videoCacheWarningGB: Double = 2.0
    @AppStorage("selectedAppIcon") private var selectedAppIcon: String = AppIconType.male.rawValue

    // Removed Media settings for maxAttachmentsPerJump and defaultPosterTime

    // State
    @State private var showingResetAlert = false
    @State private var showingExportSheet = false
    @State private var showingImportSheet = false
    @State private var showingMeasurementRanges = false
    @State private var showingMeetStandardsManager = false
    @State private var showingPurgeMediaAlert = false
    @State private var supportsAlternateIcons = false
    @State private var customAccentColor: Color = AppTheme.customAccentColor
    @AppStorage("useCustomAccentColor") private var useCustomAccentColor: Bool = false



    var body: some View {
        NavigationStack {
            Form {
                Section(header: Text("Profile")) {
                    NavigationLink(destination: AthleteProfileView()) {
                        Label {
                            Text("Athlete Profile")
                        } icon: {
                            PoleVaultIcon(size: CGSize(width: 24, height: 24))
                        }
                    }
                }

                Section(header: Text("Units")) {
                    Toggle("Use Metric System as Primary", isOn: $useMetricSystem)
                        .onChange(of: useMetricSystem) { oldValue, newValue in
                            // Force refresh views that depend on this setting
                            NotificationCenter.default.post(name: NSNotification.Name("UnitPreferenceChanged"), object: nil)

                            // Sync to iCloud key-value store
                            NSUbiquitousKeyValueStore.default.set(newValue, forKey: "useMetricSystem")
                            NSUbiquitousKeyValueStore.default.synchronize()
                        }

                    Toggle("Show Secondary Units", isOn: $showSecondaryUnits)
                        .onChange(of: showSecondaryUnits) { oldValue, newValue in
                            // Force refresh views that depend on this setting
                            NotificationCenter.default.post(name: NSNotification.Name("UnitPreferenceChanged"), object: nil)

                            // Sync to iCloud key-value store
                            NSUbiquitousKeyValueStore.default.set(newValue, forKey: "showSecondaryUnits")
                            NSUbiquitousKeyValueStore.default.synchronize()
                        }
                }

                Section(header: Text("App Icon")) {
                    Text("Change the app icon. This will also change the default theme colors.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .padding(.bottom, 5)

                    if !AppIconManager.shared.supportsAlternateIcons {
                        Text("Your device doesn't support changing app icons.")
                            .font(.caption)
                            .foregroundColor(.red)
                            .padding(.bottom, 5)
                    }

                    ForEach(AppIconType.allCases, id: \.self) { iconType in
                        Button(action: {
                            // Use the AppIconManager to change the app icon
                            AppIconManager.shared.changeAppIcon(to: iconType)
                        }) {
                            HStack {
                                // App icon preview
                                Image(iconType.iconPreviewName)
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(width: 60, height: 60)
                                    .cornerRadius(12)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                                    )

                                VStack(alignment: .leading) {
                                    Text(iconType.displayName)

                                    Text("Default \(iconType.themeColor) theme")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                .padding(.leading, 10)

                                Spacer()

                                if selectedAppIcon == iconType.rawValue && !useCustomAccentColor {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(AppTheme.accentColor)
                                }
                            }
                        }
                        .foregroundColor(.primary)
                    }
                }

                Section(header: Text("Custom Theme Color")) {
                    Toggle("Use Custom Color", isOn: $useCustomAccentColor)
                        .onChange(of: useCustomAccentColor) { oldValue, newValue in
                            if newValue {
                                // Save the current color when enabling custom color
                                AppTheme.saveCustomAccentColor(customAccentColor)
                            }
                            // Notify that theme has changed
                            NotificationCenter.default.post(name: NSNotification.Name("AppThemeChanged"), object: nil)
                        }

                    if useCustomAccentColor {
                        ColorPicker("Accent Color", selection: $customAccentColor)
                            .onChange(of: customAccentColor) { oldValue, newValue in
                                // Save the new color
                                AppTheme.saveCustomAccentColor(newValue)
                                // Notify that theme has changed
                                NotificationCenter.default.post(name: NSNotification.Name("AppThemeChanged"), object: nil)
                            }

                        HStack {
                            Text("Preview")
                            Spacer()
                            Circle()
                                .fill(customAccentColor)
                                .frame(width: 30, height: 30)
                        }
                    }
                }

                Section(header: Text("Measurement Ranges")) {
                    Button {
                        showingMeasurementRanges = true
                    } label: {
                        HStack {
                            Label("Custom Measurement Ranges", systemImage: "ruler")
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(.secondary)
                        }
                    }
                    .foregroundColor(.primary)

                    Button {
                        showingMeetStandardsManager = true
                    } label: {
                        HStack {
                            Label("Meet Standards Manager", systemImage: "chart.bar.doc.horizontal")
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(.secondary)
                        }
                    }
                    .foregroundColor(.primary)
                }

                Section(header: Text("Session Defaults")) {
                    Picker("Default Session Type", selection: $defaultSessionType) {
                        Text("Practice").tag("Practice")
                        Text("Meet").tag("Meet")
                    }
                }



                // Media Settings section removed

                Section(header: Text("Media Links")) {
                    // Storage usage information
                    StorageUsageView()

                    // Media purge button
                    Button(action: {
                        showingPurgeMediaAlert = true
                    }) {
                        Label("Clear Media Links", systemImage: "trash")
                            .foregroundColor(.red)
                    }

                    Text("This will remove all links to photos and videos in your Photos library.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Section(header: Text("iCloud Sync")) {
                    CloudKitStatusView()
                }

                Section(header: Text("Data Management")) {
                    Button(action: {
                        showingExportSheet = true
                    }) {
                        Label("Export Data", systemImage: "square.and.arrow.up")
                    }

                    Button(action: {
                        showingImportSheet = true
                    }) {
                        Label("Import Data", systemImage: "square.and.arrow.down")
                    }

                    Button(action: {
                        showingResetAlert = true
                    }) {
                        Label("Reset All Data", systemImage: "trash")
                            .foregroundColor(.red)
                    }

                    Text("This will reset the database structure and CloudKit sync state to resolve technical issues. Your data will be deleted from this device. This action cannot be undone.")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Section(header: Text("About")) {
                    HStack {
                        Text("Version")
                        Spacer()
                        if let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String,
                           let build = Bundle.main.infoDictionary?["CFBundleVersion"] as? String {
                            Text("\(version) (\(build))")
                                .foregroundColor(.secondary)
                        } else {
                            Text("1.0.0")
                                .foregroundColor(.secondary)
                        }
                    }

                    NavigationLink(destination: PrivacyPolicyView()) {
                        Label("Privacy Policy", systemImage: "lock.shield")
                    }

                    NavigationLink(destination: SupportView()) {
                        Label("Support", systemImage: "questionmark.circle")
                    }

                    Button(action: {
                        if let url = URL(string: "mailto:<EMAIL>") {
                            UIApplication.shared.open(url)
                        }
                    }) {
                        Label("Contact Us", systemImage: "envelope")
                    }
                }
            }
            .navigationTitle("Settings")
            .alert("Reset All Data", isPresented: $showingResetAlert) {
                Button("Cancel", role: .cancel) { }
                Button("Reset", role: .destructive) {
                    resetAllData()
                }
            } message: {
                Text("This will reset the database structure and delete all your data. It will fix sync issues and Core Data model conflicts. Your media files in Photos will not be affected. This action cannot be undone.")
            }
            .alert("Clear Media Links", isPresented: $showingPurgeMediaAlert) {
                Button("Cancel", role: .cancel) { }
                Button("Clear", role: .destructive) {
                    purgeMediaLinks()
                }
            } message: {
                Text("This will remove all links to photos and videos in your Photos library. Your original media files will remain in your Photos library. This action cannot be undone.")
            }
            .sheet(isPresented: $showingExportSheet) {
                NavigationStack {
                    List {
                        Section(header: Text("Export Options")) {
                            NavigationLink(destination: ExportAllView()) {
                                Label("Export All Data", systemImage: "square.and.arrow.up")
                            }

                            Text("Or select a session to export:")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Section {
                            SessionListForExport()
                        }
                    }
                    .navigationTitle("Export Data")
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .cancellationAction) {
                            Button("Cancel") {
                                showingExportSheet = false
                            }
                        }
                    }
                }
            }
            .sheet(isPresented: $showingImportSheet) {
                ImportDataView()
            }
            .sheet(isPresented: $showingMeasurementRanges) {
                MeasurementRangeView()
            }
            .sheet(isPresented: $showingMeetStandardsManager) {
                MeetStandardsManagerView()
            }

            .onAppear {
                checkAlternateIconsSupport()
            }
        }
    }



    private func resetAllData() {
        // Show a loading indicator
        let alert = UIAlertController(
            title: "Resetting Data",
            message: "Please wait while the database is being reset...",
            preferredStyle: .alert
        )

        // Present the alert
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            rootViewController.present(alert, animated: true)
        }

        // Create a local reference to the alert
        let alertRef = alert

        // Create a completion handler for reset success
        let successHandler: (Notification) -> Void = { _ in
            // Dismiss the loading indicator
            DispatchQueue.main.async {
                alertRef.dismiss(animated: true) {
                    // Show a success message with clear restart instructions
                    let restartAlert = UIAlertController(
                        title: "⚠️ RESTART REQUIRED ⚠️",
                        message: "The app's data has been reset.\n\nIMPORTANT: You MUST completely close the app by swiping it up from the app switcher, then restart it for the changes to take effect.\n\nIf you don't restart, the app will crash when you try to use it.",
                        preferredStyle: .alert
                    )

                    restartAlert.addAction(UIAlertAction(title: "I'll Restart Now", style: .default, handler: nil))

                    // Present the alert
                    if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                       let rootViewController = windowScene.windows.first?.rootViewController {
                        rootViewController.present(restartAlert, animated: true)
                    }
                }
            }
        }

        // Create a completion handler for reset failure
        let failureHandler: (Notification) -> Void = { _ in
            // Dismiss the loading indicator
            DispatchQueue.main.async {
                alertRef.dismiss(animated: true) {
                    // Show an error message
                    self.showAlert(title: "Reset Failed", message: "Failed to reset the database. Please try again.")
                }
            }
        }

        // Set up observers for reset completion
        let successObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name("CoreDataResetCompleted"),
            object: nil,
            queue: .main,
            using: successHandler
        )

        let failureObserver = NotificationCenter.default.addObserver(
            forName: NSNotification.Name("CoreDataResetFailed"),
            object: nil,
            queue: .main,
            using: failureHandler
        )

        // Remove observers after a timeout (in case reset never completes)
        DispatchQueue.main.asyncAfter(deadline: .now() + 30.0) {
            NotificationCenter.default.removeObserver(successObserver)
            NotificationCenter.default.removeObserver(failureObserver)
        }

        // Reset relevant UserDefaults
        // Keep some user preferences like useMetricSystem, showSecondaryUnits, etc.
        // but reset app data-related UserDefaults

        // Reset meet standards
        UserDefaults.standard.removeObject(forKey: "customMeetStandards")
        UserDefaults.standard.removeObject(forKey: "nfhsStandardConfig")
        UserDefaults.standard.removeObject(forKey: "defaultMeetStandard")

        // Reset height presets
        UserDefaults.standard.removeObject(forKey: "heightPresets")

        // Reset measurement ranges
        let measurementRangeKeys = [
            "runStartMinCm", "runStartMaxCm",
            "handHoldMinCm", "handHoldMaxCm",
            "takeOffStepMinCm", "takeOffStepMaxCm",
            "standardMinCm", "standardMaxCm",
            "barHeightMinCm", "barHeightMaxCm"
        ]

        for key in measurementRangeKeys {
            UserDefaults.standard.removeObject(forKey: key)
        }

        // Reset personal best data
        UserDefaults.standard.removeObject(forKey: "personalBestHeight")

        // Reset any custom color settings
        // Note: We're keeping the useCustomAccentColor preference but resetting the actual color
        UserDefaults.standard.removeObject(forKey: "customAccentColorHex")

        // Synchronize to ensure all changes are saved
        UserDefaults.standard.synchronize()

        // Reset any managers that might be caching data
        MeetStandardsManager.shared.resetToDefaults()

        // Use the CoreDataResetUtility to reset the Core Data store
        _ = CoreDataResetUtility.resetCoreDataStore()
    }

    private func checkAlternateIconsSupport() {
        // Check if the device supports alternate icons
        supportsAlternateIcons = AppIconManager.shared.supportsAlternateIcons
    }

    /// Removes all media links from jumps
    private func purgeMediaLinks() {
        // Use the MediaStorageManager to clear all media links
        let linksRemoved = MediaStorageManager.shared.clearAllMediaLinks()

        // Show a confirmation alert with the result
        let message = "Removed \(linksRemoved) media links."
        showAlert(title: "Media Links Cleared", message: message)

        // The MediaStorageManager already posts the necessary notifications,
        // so we don't need to post them again here
    }



    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(
            title: title,
            message: message,
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "OK", style: .default))

        // Present the alert
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let rootViewController = windowScene.windows.first?.rootViewController {
            rootViewController.present(alert, animated: true)
        }
    }

    /// Restarts the app by recreating the root view controller
    private func restartApp() {
        // Get the current window scene
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {

            // Set the flag to indicate that Core Data needs to be reinitialized
            UserDefaults.standard.set(true, forKey: "needsCoreDataReinitialize")

            // Create a temporary loading view
            let loadingView = UIHostingController(
                rootView:
                    ZStack {
                        Color(.systemBackground).edgesIgnoringSafeArea(.all)
                        VStack {
                            ProgressView()
                                .scaleEffect(1.5)
                                .padding()
                            Text("Restarting app...")
                                .font(.headline)
                                .padding()
                        }
                    }
            )

            // Show loading view first
            UIView.transition(with: window,
                              duration: 0.3,
                              options: .transitionCrossDissolve,
                              animations: {
                window.rootViewController = loadingView
            }) { _ in
                // After a delay, create a new ContentView with the reinitialized Core Data stack
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    // Create a new ContentView
                    let contentView = ContentView()

                    // Create a new root view controller
                    let newRootViewController = UIHostingController(rootView: contentView)

                    // Apply a transition animation
                    UIView.transition(with: window,
                                      duration: 0.3,
                                      options: .transitionCrossDissolve,
                                      animations: {
                        // Replace the loading view with the new root view controller
                        window.rootViewController = newRootViewController
                    })
                }
            }
        }
    }
}

/// Athlete Profile View for creating and editing athlete information
struct AthleteProfileView: View {
    @Environment(\.managedObjectContext) private var viewContext
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme

    @FetchRequest private var athletes: FetchedResults<Athlete>

    @State private var name = ""
    @State private var dateOfBirth = Date()
    @State private var dominantHand = "Right"
    @State private var showingNameAlert = false
    @State private var isNewUser = false
    @State private var showingConfetti = false

    init() {
        // Initialize the fetch request using the helper
        _athletes = FetchRequest(fetchRequest: CoreDataFetchRequestHelper.createAthleteFetchRequest(
            context: PersistenceController.shared.container.viewContext,
            sortDescriptors: [NSSortDescriptor(keyPath: \Athlete.name, ascending: true)]
        ))
    }

    var body: some View {
        ZStack {
            Form {
                // Welcome section for new users
                if isNewUser {
                    Section {
                        VStack(alignment: .center, spacing: 15) {
                            PoleVaultIcon(size: CGSize(width: 80, height: 80), foregroundColor: AppTheme.accentColor)
                                .padding(.top, 10)

                            Text("Welcome to PoleVaultLogPro!")
                                .font(.headline)
                                .multilineTextAlignment(.center)

                            Text("Let's set up your athlete profile to get started.")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.bottom, 10)
                        }
                        .frame(maxWidth: .infinity)
                        .listRowBackground(Color.clear)
                    }
                }

                Section(header: Text("Personal Information")) {
                    TextField("Name", text: $name)
                        .autocapitalization(.words)
                        .disableAutocorrection(true)

                    DatePicker("Date of Birth", selection: $dateOfBirth, displayedComponents: [.date])

                    Picker("Dominant Hand", selection: $dominantHand) {
                        Text("Right").tag("Right")
                        Text("Left").tag("Left")
                    }
                }

                if let athlete = athletes.first {
                    Section(header: Text("Equipment")) {
                        NavigationLink(destination: PoleListView(athlete: athlete)) {
                            Label {
                                Text("Manage Poles")
                            } icon: {
                                PoleVaultIcon(size: CGSize(width: 24, height: 24))
                            }
                        }
                    }
                }

                Section {
                    Button(isNewUser ? "Create Profile" : "Save") {
                        if name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                            showingNameAlert = true
                        } else {
                            saveProfile()
                        }
                    }
                    .frame(maxWidth: .infinity, alignment: .center)
                    .foregroundColor(AppTheme.accentColor)
                }

                if isNewUser {
                    Section {
                        Text("You can always update your profile later in Settings.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                            .frame(maxWidth: .infinity)
                    }
                    .listRowBackground(Color.clear)
                }
            }

            // Confetti overlay
            if showingConfetti {
                ConfettiView(isShowing: $showingConfetti)
                    .allowsHitTesting(false)
            }
        }
        .navigationTitle(isNewUser ? "Create Athlete Profile" : "Athlete Profile")
        .alert("Name Required", isPresented: $showingNameAlert) {
            Button("OK", role: .cancel) { }
        } message: {
            Text("Please enter a name for your athlete profile.")
        }
        .onAppear {
            // Check if this is a new user (no athletes exist)
            isNewUser = athletes.isEmpty

            if let athlete = athletes.first {
                // Existing athlete - load their data
                name = athlete.name ?? ""
                dateOfBirth = athlete.dateOfBirth ?? Date()
                dominantHand = athlete.dominantHand ?? "Right"
            } else {
                // New user - set default values
                let calendar = Calendar.current
                let defaultDate = calendar.date(byAdding: .year, value: -16, to: Date()) ?? Date()
                dateOfBirth = defaultDate
            }
        }
    }

    private func saveProfile() {
        withAnimation {
            let isCreatingNew = athletes.isEmpty

            if let athlete = athletes.first {
                // Update existing athlete
                athlete.name = name
                athlete.dateOfBirth = dateOfBirth
                athlete.dominantHand = dominantHand
            } else {
                // Create new athlete using the helper
                _ = Athlete.create(
                    in: viewContext,
                    name: name,
                    dateOfBirth: dateOfBirth,
                    dominantHand: dominantHand
                )

                // Show confetti for new users
                showingConfetti = true
            }

            do {
                try viewContext.save()

                // If creating a new profile, wait for confetti animation before dismissing
                if isCreatingNew {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                        dismiss()
                    }
                } else {
                    dismiss()
                }
            } catch {
                let nsError = error as NSError
                print("Error saving athlete: \(nsError), \(nsError.userInfo)")
            }
        }
    }
}



#Preview {
    SettingsView()
        .environment(\.managedObjectContext, PersistenceController(inMemory: true).container.viewContext)
}
