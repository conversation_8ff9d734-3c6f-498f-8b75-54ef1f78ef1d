import SwiftUI
import CoreData
import PhotosUI

struct JumpMediaListView: View {
    @Environment(\.managedObjectContext) private var viewContext

    @FetchRequest private var jumpsWithMedia: FetchedResults<Jump>

    // We don't need these state variables anymore with the new navigation approach
    @State private var searchText = ""
    @State private var refreshID = UUID() // State to force refresh when media files change

    // Listen for data reset and CloudKit sync notifications
    init() {
        // We need to use a different approach for struct views
        // The notification will be handled by the parent view controller refreshing the view

        // Initialize the fetch request using the helper
        _jumpsWithMedia = FetchRequest(fetchRequest: CoreDataFetchRequestHelper.createJumpFetchRequest(
            context: PersistenceController.shared.container.viewContext,
            sortDescriptors: [
                NSSortDescriptor(keyPath: \Jump.session?.date, ascending: false),
                NSSortDescriptor(keyPath: \Jump.order, ascending: true)
            ],
            predicate: NSPredicate(format: "mediaItems.@count > 0")
        ))
    }

    // Setup notification observers when the view appears
    private func setupNotificationObservers() {
        // Listen for CloudKit data changes
        NotificationCenter.default.addObserver(
            forName: CloudKitSyncManager.NotificationName.cloudKitDataChanged,
            object: nil,
            queue: .main
        ) { [self] _ in
            print("JumpMediaListView received CloudKit data change notification")
            refreshID = UUID()
        }
    }

    var body: some View {
        NavigationStack {
            ZStack {
                // Background watermark
                WatermarkView()

                if jumpsWithMedia.isEmpty {
                    // Empty state with ScrollView
                    ScrollView {
                        VStack(spacing: 20) {
                            PoleVaultIcon(size: CGSize(width: 80, height: 80), foregroundColor: AppTheme.accentColor)

                            Text("No Media Attachments")
                                .font(.title2)
                                .fontWeight(.bold)

                            Text("Jumps with photos or videos will appear here")
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal)
                        }
                        .padding(.top, 40)
                        .padding(.horizontal)
                    }
                } else {
                    mediaListView
                }
            }
            .id(refreshID) // Force refresh when refreshID changes
            .navigationTitle("Jump Media")
            .searchable(text: $searchText, prompt: "Search jumps")
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("MediaFilesChanged"))) { _ in
                // Refresh the view when media files are changed
                print("Refreshing JumpMediaListView due to media files change")
                refreshID = UUID() // Force a refresh
            }
            .onAppear {
                setupNotificationObservers()
            }
            // We don't need the sheet anymore as we're using NavigationLink
        }
    }

    private var mediaListView: some View {
        List {
            ForEach(groupedJumps.keys.sorted(by: >), id: \.self) { date in
                Section(header: Text(formatDate(date))) {
                    ForEach(groupedJumps[date] ?? [], id: \.self) { jump in
                        NavigationLink(destination: MediaBrowserView(jump: jump)) {
                            JumpMediaRow(jump: jump)
                        }
                    }
                }
            }
        }
    }

    private var filteredJumps: [Jump] {
        if searchText.isEmpty {
            return Array(jumpsWithMedia)
        } else {
            return jumpsWithMedia.filter { jump in
                let heightString = AppTheme.useMetricSystem ?
                    HeightConverter.cmToMetersString(jump.barHeightCm) :
                    HeightConverter.cmToFeetInchesString(jump.barHeightCm)

                let sessionTitle = jump.session?.title ?? ""
                let comment = jump.comment ?? ""

                return heightString.localizedCaseInsensitiveContains(searchText) ||
                       sessionTitle.localizedCaseInsensitiveContains(searchText) ||
                       comment.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    private var groupedJumps: [Date: [Jump]] {
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.timeStyle = .none

        var result: [Date: [Jump]] = [:]

        for jump in filteredJumps {
            if let session = jump.session {
                let date = Date(timeIntervalSinceReferenceDate: session.date)
                let calendar = Calendar.current
                let components = calendar.dateComponents([.year, .month, .day], from: date)
                if let normalizedDate = calendar.date(from: components) {
                    if result[normalizedDate] == nil {
                        result[normalizedDate] = []
                    }
                    result[normalizedDate]?.append(jump)
                }
            }
        }

        return result
    }

    private func formatDate(_ date: Date) -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateStyle = .medium
        dateFormatter.timeStyle = .none
        return dateFormatter.string(from: date)
    }
}

struct JumpMediaRow: View {
    @ObservedObject var jump: Jump
    @State private var refreshID = UUID() // State to force refresh when media files change

    var body: some View {
        VStack(spacing: 0) {
            HStack(spacing: 12) {
                // Media thumbnail
                let mediaItems = jump.getMediaItemsAsStructs()

                // Validate media items
                let validMedia: JumpMediaStruct? = validateMediaItems(mediaItems)

                if let validMedia = validMedia {
                    // Use the validated JumpMediaStruct model
                    MediaThumbnailView(
                        media: validMedia,
                        size: CGSize(width: 80, height: 80)
                    )
                } else {
                    // Placeholder if no media is available
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color.gray.opacity(0.2))
                        .frame(width: 80, height: 80)
                        .overlay(
                            Image(systemName: "photo")
                                .foregroundColor(.gray)
                        )
                }

                VStack(alignment: .leading, spacing: 4) {
                    // Session title only
                    if let session = jump.session {
                        Text(session.title ?? "Untitled Session")
                            .font(.headline)
                    }

                    // Comment across the top (if available)
                    if let comment = jump.comment, !comment.isEmpty {
                        Text(comment)
                            .font(.headline)
                            .foregroundColor(.primary)
                            .lineLimit(1)
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(.bottom, 4)
                    }

                    // Height, result, and bar/bungee indicator
                    HStack {
                        Text(heightString)
                            .font(.subheadline)
                            .foregroundColor(AppTheme.accentColor)

                        Text(jump.result?.capitalized ?? "Unknown")
                            .font(.caption)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(resultColor.opacity(0.2))
                            .foregroundColor(resultColor)
                            .cornerRadius(4)

                        // Attempt indicator
                        Text("Attempt \(jump.attemptIndex)")
                            .font(.caption)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.gray.opacity(0.2))
                            .foregroundColor(.gray)
                            .cornerRadius(4)

                        Spacer()

                        // Bar/Bungee indicator
                        Image(systemName: jump.useBar ? "minus" : "water.waves")
                            .font(.caption)
                            .foregroundColor(jump.useBar ? .primary : .blue)
                    }

                    // Practice/Meet indicator with pole name on the same line
                    HStack {
                        // Practice/Meet indicator first
                        if let session = jump.session {
                            Text(session.type?.capitalized ?? "Practice")
                                .font(.caption)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(sessionTypeColor.opacity(0.2))
                                .foregroundColor(sessionTypeColor)
                                .cornerRadius(4)
                        }

                        // Then pole name
                        if let pole = jump.pole, let poleName = pole.name {
                            if let colorHex = pole.color, !colorHex.isEmpty {
                                Text(poleName)
                                    .font(.caption)
                                    .foregroundColor(Color(hex: colorHex))
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(Color.gray.opacity(0.1))
                                    .cornerRadius(4)
                            } else {
                                Text(poleName)
                                    .font(.caption)
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(Color.gray.opacity(0.1))
                                    .cornerRadius(4)
                            }
                        }

                        Spacer()
                    }
                    .padding(.top, 2)

                    // Comment removed as it's now shown at the top

                    // Technical details toggle removed as requested
                }

                Spacer()

                Image(systemName: "play.circle.fill")
                    .foregroundColor(AppTheme.accentColor)
                    .font(.title2)
            }

            // Technical details section removed as requested
        }
        .padding(.vertical, 4)
        .id(refreshID) // Force refresh when refreshID changes
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("MediaFilesChanged"))) { _ in
            // Refresh the view when media files are changed
            print("Refreshing JumpMediaRow due to media files change")
            refreshID = UUID() // Force a refresh
        }
    }

    // Helper view for technical details
    private struct TechnicalDetailRow: View {
        let label: String
        let value: String

        var body: some View {
            HStack(spacing: 4) {
                Text(label + ":")
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text(value)
                    .font(.caption)
                    .foregroundColor(.primary)
            }
        }
    }

    private var heightString: String {
        if AppTheme.useMetricSystem {
            return HeightConverter.cmToMetersString(jump.barHeightCm)
        } else {
            return HeightConverter.cmToFeetInchesString(jump.barHeightCm)
        }
    }

    private var resultColor: Color {
        switch jump.result {
        case "make":
            return .green
        case "miss":
            return .red
        case "pass":
            return .gray
        default:
            return .gray
        }
    }

    private var sessionTypeColor: Color {
        if let type = jump.session?.type?.lowercased() {
            return type == "meet" ? .orange : .blue
        }
        return .blue
    }

    /// Format technical value with appropriate units
    private func formatTechnicalValue(_ value: Double) -> String {
        if value < 0.1 {
            return "N/A"
        }

        if AppTheme.useMetricSystem {
            return String(format: "%.2f m", value / 100)
        } else {
            // Use appropriate formatter based on the measurement type
            if abs(value - jump.runStartCm) < 0.1 {
                return HeightConverter.formatStepMeasurement(cm: value)
            } else if abs(value - jump.handHoldCm) < 0.1 {
                return HeightConverter.formatHandHoldMeasurement(cm: value)
            } else if abs(value - jump.takeOffStepCm) < 0.1 {
                return HeightConverter.formatStepMeasurement(cm: value)
            } else {
                return HeightConverter.cmToFeetInchesString(value)
            }
        }
    }

    /// Validates a list of media items and returns the first valid one
    private func validateMediaItems(_ mediaItems: [JumpMediaStruct]) -> JumpMediaStruct? {
        // If there are no media items, return nil
        guard !mediaItems.isEmpty else { return nil }

        // Check the first media item
        let firstMedia = mediaItems.first!
        let (isAvailable, repairedMedia) = firstMedia.validateMedia()

        if isAvailable {
            // Use repaired media if available
            return repairedMedia ?? firstMedia
        }

        // If the first media is not available, check the rest
        for media in mediaItems.dropFirst() {
            let (isAvailable, repairedMedia) = media.validateMedia()
            if isAvailable {
                return repairedMedia ?? media
            }
        }

        // If no valid media was found, return nil
        return nil
    }
}

#Preview {
    JumpMediaListView()
        .environment(\.managedObjectContext, PersistenceController(inMemory: true).container.viewContext)
}
