import Foundation
import CloudKit
import CoreData

/// A wrapper for CloudKit diagnostic tools that clearly separates them from core functionality
/// This class provides access to diagnostic tools without interfering with the standard CloudKit sync
class CloudKitDiagnosticWrapper {
    // MARK: - Singleton

    /// Shared instance
    static let shared = CloudKitDiagnosticWrapper()

    // MARK: - Properties

    /// The CloudKit diagnostic manager
    private let diagnosticManager = CloudKitDiagnosticManager.shared

    // MARK: - Initialization

    private init() {
        print("CloudKitDiagnosticWrapper initialized - DIAGNOSTIC USE ONLY")
    }

    // MARK: - Public Methods

    /// Runs diagnostics to compare local and cloud records
    /// - Parameters:
    ///   - entityType: The entity type to compare
    ///   - context: The managed object context
    ///   - completion: Completion handler with comparison results or error
    func runDiagnostics(
        forEntityType entityType: String,
        in context: NSManagedObjectContext,
        completion: @escaping (ComparisonResult?, Error?) -> Void
    ) {
        print("🔍 Running CloudKit diagnostics for entity type: \(entityType) - DIAGNOSTIC USE ONLY")
        diagnosticManager.compareRecords(forEntityType: entityType, in: context, completion: completion)
    }

    /// Gets all available entity types for diagnostics
    /// - Returns: Array of entity type names
    func getEntityTypes() -> [String] {
        return diagnosticManager.getEntityTypes()
    }



    /// Checks if the Core Data CloudKit zone exists
    /// - Parameter completion: Completion handler with boolean indicating if zone exists
    func checkCoreDataCloudKitZoneExists(completion: @escaping (Bool) -> Void) {
        print("🔍 Checking if Core Data CloudKit zone exists - DIAGNOSTIC USE ONLY")
        diagnosticManager.checkCoreDataCloudKitZoneExists(completion: completion)
    }
}
