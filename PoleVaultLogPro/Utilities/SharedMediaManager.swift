import Foundation
import Photos
import PhotosUI
import UIKit
import CoreData
import SwiftUI

/// Manages media sharing across devices using iCloud Shared Albums
class SharedMediaManager {
    // MARK: - Singleton

    /// Shared instance of the SharedMediaManager
    static let shared = SharedMediaManager()

    // MARK: - Properties

    /// The name of the shared album used by the app
    private let sharedAlbumName = "PoleVaultLogPro Media"

    /// The PHCachingImageManager used for image requests
    let cachingImageManager = PHCachingImageManager()

    /// The shared album collection
    private var sharedAlbum: PHAssetCollection?

    /// Default poster time for videos (30% of the way through)
    let defaultPosterTime: Double = 0.3

    // MARK: - Initialization

    private init() {
        // Find or create the shared album when the manager is initialized
        findOrCreateSharedAlbum()
    }

    // MARK: - Shared Album Management

    /// Finds the existing shared album or creates a new one if it doesn't exist
    private func findOrCreateSharedAlbum() {
        // First, try to find an existing shared album with our name
        let fetchOptions = PHFetchOptions()
        fetchOptions.predicate = NSPredicate(format: "title = %@", sharedAlbumName)

        // Look in regular albums first
        var collections = PHAssetCollection.fetchAssetCollections(with: .album, subtype: .albumRegular, options: fetchOptions)
        if let collection = collections.firstObject {
            self.sharedAlbum = collection
            print("Found existing regular album: \(sharedAlbumName)")

            // In iOS 18, we can't reliably check if an album is shared
            // Just make sure the album exists and is properly set up
            makeAlbumShared(collection)
            return
        }

        // Then look in shared albums
        collections = PHAssetCollection.fetchAssetCollections(with: .album, subtype: .any, options: fetchOptions)
        if let collection = collections.firstObject {
            self.sharedAlbum = collection
            print("Found existing shared album: \(sharedAlbumName)")
            return
        }

        // If we didn't find an existing album, create a new one
        createSharedAlbum()
    }

    /// Creates a new shared album for the app
    private func createSharedAlbum() {
        // Request permission to access the photo library if needed
        PHPhotoLibrary.requestAuthorization { [weak self] status in
            guard let self = self, status == .authorized else {
                print("Photo library access not authorized")
                return
            }

            // Create a new album
            var albumPlaceholder: String?

            PHPhotoLibrary.shared().performChanges({
                let createAlbumRequest = PHAssetCollectionChangeRequest.creationRequestForAssetCollection(withTitle: self.sharedAlbumName)
                albumPlaceholder = createAlbumRequest.placeholderForCreatedAssetCollection.localIdentifier
            }) { success, error in
                if success, let albumPlaceholder = albumPlaceholder {
                    // Find the album we just created
                    let fetchOptions = PHFetchOptions()
                    fetchOptions.predicate = NSPredicate(format: "localIdentifier = %@", albumPlaceholder)
                    let collections = PHAssetCollection.fetchAssetCollections(with: .album, subtype: .albumRegular, options: fetchOptions)

                    if let collection = collections.firstObject {
                        // Make the album shared
                        self.makeAlbumShared(collection)
                    }
                } else {
                    print("Error creating album: \(String(describing: error))")
                }
            }
        }
    }

    /// Makes an album shared if it's not already
    private func makeAlbumShared(_ collection: PHAssetCollection) {
        // Note: PHAssetCollectionShareRequest is not available in iOS 18
        // We'll use a simpler approach to check if the album is already shared

        PHPhotoLibrary.shared().performChanges({
            // We can't directly share an album programmatically in iOS 18
            // The user would need to manually share the album from the Photos app

            // Instead, we'll just add a placeholder asset to ensure the album exists
            _ = PHAssetCollectionChangeRequest(for: collection)
            // No need to actually add anything here

        }) { success, error in
            if success {
                print("Successfully shared album: \(self.sharedAlbumName)")

                // Update our reference to the shared album
                let fetchOptions = PHFetchOptions()
                fetchOptions.predicate = NSPredicate(format: "title = %@", self.sharedAlbumName)
                let collections = PHAssetCollection.fetchAssetCollections(with: .album, subtype: .any, options: fetchOptions)

                if let collection = collections.firstObject {
                    self.sharedAlbum = collection
                }
            } else {
                print("Error sharing album: \(String(describing: error))")
            }
        }
    }

    // MARK: - Media Processing

    /// Process a picked asset and add it to the shared album
    /// - Parameter localAssetId: The local asset identifier from Photos
    /// - Returns: A JumpMediaStruct with the shared asset identifier
    func processMediaItem(withLocalIdentifier localAssetId: String) async -> JumpMediaStruct? {
        // Use the provided local asset identifier
        // Fetch the asset
        let fetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [localAssetId], options: nil)

        if let asset = fetchResult.firstObject {
            // Determine the media type
            let mediaType: JumpMediaStruct.MediaType = asset.mediaType == .video ? .video : .photo

            // Add the asset to the shared album and get the shared identifier
            if let sharedIdentifier = await addAssetToSharedAlbum(asset) {
                return JumpMediaStruct(
                    type: mediaType,
                    assetIdentifier: sharedIdentifier, // Use the shared identifier
                    posterTime: mediaType == .video ? defaultPosterTime : nil
                )
            } else {
                // Fallback to using the local identifier if we couldn't add to shared album
                print("Warning: Using local asset ID as fallback")
                return JumpMediaStruct(
                    type: mediaType,
                    assetIdentifier: localAssetId,
                    posterTime: mediaType == .video ? defaultPosterTime : nil
                )
            }
        }

        return nil
    }

    /// Adds an asset to the shared album and returns the shared identifier
    /// - Parameter asset: The PHAsset to add to the shared album
    /// - Returns: The shared identifier for the asset
    private func addAssetToSharedAlbum(_ asset: PHAsset) async -> String? {
        // Make sure we have a shared album
        guard let sharedAlbum = self.sharedAlbum else {
            // Try to find or create the shared album
            findOrCreateSharedAlbum()

            // If we still don't have a shared album, return nil
            guard let sharedAlbum = self.sharedAlbum else {
                print("No shared album available")
                return nil
            }

            return await addAssetToSharedAlbum(asset, collection: sharedAlbum)
        }

        return await addAssetToSharedAlbum(asset, collection: sharedAlbum)
    }

    /// Adds an asset to a specific shared album collection
    /// - Parameters:
    ///   - asset: The PHAsset to add
    ///   - collection: The PHAssetCollection to add the asset to
    /// - Returns: The shared identifier for the asset
    private func addAssetToSharedAlbum(_ asset: PHAsset, collection: PHAssetCollection) async -> String? {
        return await withCheckedContinuation { continuation in
            // Check if the asset is already in the shared album
            let fetchOptions = PHFetchOptions()
            fetchOptions.predicate = NSPredicate(format: "localIdentifier = %@", asset.localIdentifier)
            let assetsInAlbum = PHAsset.fetchAssets(in: collection, options: fetchOptions)

            if let existingAsset = assetsInAlbum.firstObject {
                // Asset is already in the shared album, get its shared identifier
                if let sharedIdentifier = getSharedIdentifier(for: existingAsset) {
                    continuation.resume(returning: sharedIdentifier)
                    return
                }
            }

            // Add the asset to the shared album
            PHPhotoLibrary.shared().performChanges({
                let albumChangeRequest = PHAssetCollectionChangeRequest(for: collection)
                // No need to create placeholders, just add the asset directly
                albumChangeRequest?.addAssets([asset] as NSFastEnumeration)

            }) { success, error in
                if success {
                    // Fetch the asset in the shared album
                    let fetchOptions = PHFetchOptions()
                    fetchOptions.predicate = NSPredicate(format: "localIdentifier = %@", asset.localIdentifier)
                    let assetsInAlbum = PHAsset.fetchAssets(in: collection, options: fetchOptions)

                    if let sharedAsset = assetsInAlbum.firstObject {
                        // Get the shared identifier
                        if let sharedIdentifier = self.getSharedIdentifier(for: sharedAsset) {
                            continuation.resume(returning: sharedIdentifier)
                            return
                        }
                    }
                }

                print("Error adding asset to shared album: \(String(describing: error))")
                continuation.resume(returning: nil)
            }
        }
    }

    /// Gets the shared identifier for an asset
    /// - Parameter asset: The PHAsset to get the shared identifier for
    /// - Returns: The shared identifier
    private func getSharedIdentifier(for asset: PHAsset) -> String? {
        // In iOS 18, we can't reliably get a cloud identifier
        // Just use the local identifier as the shared identifier
        return asset.localIdentifier
    }

    // The getAssetIdentifier method has been removed since we now get the asset identifier directly from MediaStorageManager

    // MARK: - Asset Retrieval

    /// Fetches an asset using its shared identifier
    /// - Parameter sharedIdentifier: The shared identifier of the asset
    /// - Returns: The PHAsset if found, nil otherwise
    func fetchSharedAsset(with sharedIdentifier: String) -> PHAsset? {
        // In iOS 18, we're using the local identifier as the shared identifier
        // So we can directly fetch the asset using the identifier
        let localFetchResult = PHAsset.fetchAssets(withLocalIdentifiers: [sharedIdentifier], options: nil)
        if let asset = localFetchResult.firstObject {
            return asset
        }

        // If not found directly, it might be because the identifier format has changed
        // Try to find it in our shared album
        if let sharedAlbum = self.sharedAlbum {
            // Fetch all assets in the album
            let assets = PHAsset.fetchAssets(in: sharedAlbum, options: nil)

            // If we only have one asset in the album, it's likely the one we want
            if assets.count == 1 {
                return assets.firstObject
            }
        }

        // If we still can't find it, return nil
        return nil
    }
}
