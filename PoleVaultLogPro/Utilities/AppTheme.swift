import SwiftUI

/// Enum representing the available app themes
enum AppThemeType: String {
    case male = "AppIcon"
    case female = "AppIconFemale"

    /// Get the theme type from the selected app icon
    static var current: AppThemeType {
        let selectedIcon = UserDefaults.standard.string(forKey: "selectedAppIcon") ?? "AppIcon"
        return AppThemeType(rawValue: selectedIcon) ?? .male
    }
}

/// Manages the app's visual theme, including colors, fonts, and styling.
struct AppTheme {
    /// Whether to use a custom accent color
    @AppStorage("useCustomAccentColor") static var useCustomAccentColor: Bool = false

    /// The custom accent color selected by the user
    static var customAccentColor: Color {
        let colorHex = UserDefaults.standard.string(forKey: "customAccentColorHex") ?? "1E7EFF"
        return Color(hex: colorHex)
    }

    /// Save a custom accent color
    static func saveCustomAccentColor(_ color: Color) {
        // Convert Color to hex string and save
        let uiColor = UIColor(color)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0

        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)

        let hexString = String(
            format: "%02X%02X%02X",
            Int(red * 255),
            Int(green * 255),
            Int(blue * 255)
        )

        UserDefaults.standard.set(hexString, forKey: "customAccentColorHex")
    }

    /// The primary accent color of the app based on the current theme
    static var accentColor: Color {
        if useCustomAccentColor {
            return customAccentColor
        }

        switch AppThemeType.current {
        case .male:
            return Color(hex: "1E7EFF") // Electric blue
        case .female:
            return Color(hex: "FF1E7E") // Pink
        }
    }

    /// A darker shade of the accent color for gradients and highlights
    static var accentColorDark: Color {
        if useCustomAccentColor {
            return darken(customAccentColor, by: 0.2)
        }

        switch AppThemeType.current {
        case .male:
            return Color(hex: "0A5DCF") // Darker blue
        case .female:
            return Color(hex: "CF0A5D") // Darker pink
        }
    }

    /// A lighter shade of the accent color for backgrounds and secondary elements
    static var accentColorLight: Color {
        if useCustomAccentColor {
            return lighten(customAccentColor, by: 0.2)
        }

        switch AppThemeType.current {
        case .male:
            return Color(hex: "7DB6FF") // Lighter blue
        case .female:
            return Color(hex: "FF7DB6") // Lighter pink
        }
    }

    /// Darken a color by a percentage
    private static func darken(_ color: Color, by percentage: CGFloat) -> Color {
        let uiColor = UIColor(color)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0

        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)

        return Color(
            red: Double(max(red - percentage, 0)),
            green: Double(max(green - percentage, 0)),
            blue: Double(max(blue - percentage, 0))
        )
    }

    /// Lighten a color by a percentage
    private static func lighten(_ color: Color, by percentage: CGFloat) -> Color {
        let uiColor = UIColor(color)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0

        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)

        return Color(
            red: Double(min(red + percentage, 1)),
            green: Double(min(green + percentage, 1)),
            blue: Double(min(blue + percentage, 1))
        )
    }

    /// Standard corner radius for UI elements
    static let cornerRadius: CGFloat = 12

    /// Standard padding for UI elements
    static let padding: CGFloat = 16

    /// Standard animation duration
    static let animationDuration: Double = 0.3

    /// Whether to use the metric system as the primary measurement system
    @AppStorage("useMetricSystem") static var useMetricSystem: Bool = false

    /// Whether to show secondary units alongside primary units
    @AppStorage("showSecondaryUnits") static var showSecondaryUnits: Bool = true

    /// Get the appropriate jumper icon name based on the current theme
    static var jumperIconName: String {
        switch AppThemeType.current {
        case .male:
            return "MaleJumperIcon"
        case .female:
            return "FemaleJumperIcon"
        }
    }

    /// Get the appropriate pole vault icon name based on the current theme
    static var poleVaultIconName: String {
        switch AppThemeType.current {
        case .male:
            return "PoleVaultMaleIcon"
        case .female:
            return "PoleVaultFemaleIcon"
        }
    }

    /// Get the appropriate pole vault icon name for dark mode based on the current theme
    static var poleVaultIconNameDark: String {
        switch AppThemeType.current {
        case .male:
            return "PoleVaultMaleIconWhite"
        case .female:
            return "PoleVaultFemaleIconWhite"
        }
    }

    // Default measurement ranges
    static let defaultRunStartMinCm: Double = 0
    static let defaultRunStartMaxCm: Double = 3048

    static let defaultHandHoldMinCm: Double = 213
    static let defaultHandHoldMaxCm: Double = 487

    static let defaultTakeOffStepMinCm: Double = 152
    static let defaultTakeOffStepMaxCm: Double = 487

    static let defaultStandardMinCm: Double = 30
    static let defaultStandardMaxCm: Double = 80

    static let defaultBarHeightMinCm: Double = 91
    static let defaultBarHeightMaxCm: Double = 609
}

/// Extends Color to support hex string initialization
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

/// Extends View with common styling modifiers
extension View {
    /// Applies the standard card style to a view
    func cardStyle() -> some View {
        self
            .padding(AppTheme.padding)
            .background(Color(.systemBackground))
            .cornerRadius(AppTheme.cornerRadius)
            .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
    }

    /// Applies the primary button style to a view
    func primaryButtonStyle() -> some View {
        self
            .padding()
            .background(AppTheme.accentColor)
            .foregroundColor(.white)
            .cornerRadius(AppTheme.cornerRadius)
            .shadow(color: AppTheme.accentColor.opacity(0.3), radius: 5, x: 0, y: 2)
    }

    /// Applies the secondary button style to a view
    func secondaryButtonStyle() -> some View {
        self
            .padding()
            .background(Color(.systemBackground))
            .foregroundColor(AppTheme.accentColor)
            .cornerRadius(AppTheme.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: AppTheme.cornerRadius)
                    .stroke(AppTheme.accentColor, lineWidth: 1)
            )
    }
}
