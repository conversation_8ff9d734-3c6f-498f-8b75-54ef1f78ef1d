import Foundation
import CloudKit
import CoreData
import UIKit
import SwiftUI

/// Manages CloudKit diagnostic operations
class CloudKitDiagnosticManager {
    // MARK: - Singleton

    /// Shared instance
    static let shared = CloudKitDiagnosticManager()

    // MARK: - Properties

    /// Controls whether to show verbose per-record logging
    static var verboseLogging = false

    /// The CloudKit container
    private let container: CKContainer

    /// The private database
    private let privateDatabase: CKDatabase

    /// The Core Data CloudKit zone ID
    private let coreDataCloudKitZoneID: CKRecordZone.ID

    /// Entity types from the schema
    private let entityTypes = [
        "Athlete",
        "Session",
        "Jump",
        "JumpMedia",
        "Pole",
        "GridHeight",
        "SyncMarker"
    ]

    // MARK: - Initialization

    private init() {
        // Initialize with the app's CloudKit container
        container = CKContainer(identifier: AppConfiguration.cloudKitContainerID)
        privateDatabase = container.privateCloudDatabase

        // Create the Core Data CloudKit zone ID
        coreDataCloudKitZoneID = CKRecordZone.ID(zoneName: "com.apple.coredata.cloudkit.zone", ownerName: CKCurrentUserDefaultName)

        print("CloudKitDiagnosticManager initialized with container: \(AppConfiguration.cloudKitContainerID)")
        print("Using Core Data CloudKit zone: \(coreDataCloudKitZoneID.zoneName)")
    }

    // MARK: - Public Methods

    /// Fetches all CloudKit records for a specific entity type
    /// - Parameters:
    ///   - entityType: The entity type to fetch (without the "CD_" prefix)
    ///   - completion: Completion handler with results or error
    func fetchCloudRecords(
        forEntityType entityType: String,
        completion: @escaping ([CKRecord]?, Error?) -> Void
    ) {
        // Create a query for all records of this type
        let recordType = "CD_\(entityType)"
        print("🔍 Fetching CloudKit records for type: \(recordType)")

        // Use a simple predicate that matches all records
        let query = CKQuery(recordType: recordType, predicate: NSPredicate(value: true))

        // Use the Core Data CloudKit zone ID
        print("🔍 Using Core Data CloudKit zone ID: \(coreDataCloudKitZoneID.zoneName)")

        // Use CKQueryOperation
        let operation = CKQueryOperation(query: query)
        operation.zoneID = coreDataCloudKitZoneID
        operation.resultsLimit = CKQueryOperation.maximumResults

        var allRecords: [CKRecord] = []

        // Use recordMatchedBlock for iOS 15+
        operation.recordMatchedBlock = { (recordID, result) in
            switch result {
            case .success(let record):
                // Check for both prefixed and non-prefixed ID fields
                let regularId = record["id"] as? String
                let prefixedId = record["CD_id"] as? String

                #if DEBUG
                // Only log in debug builds and only if verbose logging is enabled
                if CloudKitDiagnosticManager.verboseLogging {
                    print("✅ Found record: \(record.recordID.recordName)")
                    print("   - Regular ID field: \(regularId ?? "nil")")
                    print("   - Prefixed ID field: \(prefixedId ?? "nil")")
                    print("   - All fields: \(record.allKeys().map { "\($0): \(record[$0] != nil ? "has value" : "nil")" }.joined(separator: ", "))")
                }
                #endif

                allRecords.append(record)
            case .failure(let error):
                print("❌ Error fetching record: \(error.localizedDescription)")
            }
        }

        // Use queryResultBlock for iOS 15+
        operation.queryResultBlock = { result in
            switch result {
            case .success(_):
                print("✅ Query completed successfully, found \(allRecords.count) records")
                completion(allRecords, nil)
            case .failure(let error):
                print("❌ Error fetching CloudKit records for \(entityType): \(error.localizedDescription)")
                if let ckError = error as? CKError {
                    print("❌ CKError code: \(ckError.code.rawValue), \(ckError.localizedDescription)")

                    // Check for specific error conditions
                    if ckError.code == .unknownItem {
                        print("❌ Unknown item error - record type might not exist in schema")
                    } else if ckError.code == .zoneNotFound {
                        print("❌ Zone not found error - Core Data CloudKit zone might not be initialized")

                        // Try to create the zone if it doesn't exist
                        self.createCoreDataCloudKitZone { success in
                            if success {
                                print("✅ Created Core Data CloudKit zone, retrying query")
                                self.fetchCloudRecords(forEntityType: entityType, completion: completion)
                            } else {
                                completion(nil, error)
                            }
                        }
                        return
                    } else if ckError.code == .badDatabase {
                        print("❌ Bad database error - check container ID and permissions")
                    }
                }
                completion(nil, error)
            }
        }

        print("🔄 Adding operation to database for record type: \(recordType)")
        privateDatabase.add(operation)
    }

    /// Fetches all local Core Data records for a specific entity type
    /// - Parameters:
    ///   - entityType: The entity type to fetch
    ///   - context: The managed object context
    /// - Returns: Array of managed objects or nil if an error occurred
    func fetchLocalRecords(
        forEntityType entityType: String,
        in context: NSManagedObjectContext
    ) -> [NSManagedObject]? {
        let fetchRequest = NSFetchRequest<NSManagedObject>(entityName: entityType)

        do {
            return try context.fetch(fetchRequest)
        } catch {
            print("Error fetching local records for \(entityType): \(error.localizedDescription)")
            return nil
        }
    }

    /// Compares local and cloud records for a specific entity type
    /// - Parameters:
    ///   - entityType: The entity type to compare
    ///   - context: The managed object context
    ///   - completion: Completion handler with comparison results or error
    func compareRecords(
        forEntityType entityType: String,
        in context: NSManagedObjectContext,
        completion: @escaping (ComparisonResult?, Error?) -> Void
    ) {
        print("🔍 Comparing records for entity type: \(entityType)")

        // First check if the Core Data CloudKit zone exists
        checkCoreDataCloudKitZoneExists { zoneExists in
            if !zoneExists {
                print("⚠️ Core Data CloudKit zone does not exist, creating it")
                self.createCoreDataCloudKitZone { success in
                    if !success {
                        completion(nil, NSError(domain: "CloudKitDiagnostic", code: 3, userInfo: [
                            NSLocalizedDescriptionKey: "Failed to create Core Data CloudKit zone"
                        ]))
                        return
                    }

                    // Zone created, continue with comparison
                    self.continueCompareRecords(forEntityType: entityType, in: context, completion: completion)
                }
            } else {
                // Zone exists, continue with comparison
                self.continueCompareRecords(forEntityType: entityType, in: context, completion: completion)
            }
        }
    }

    /// Continues the record comparison after checking the zone
    /// - Parameters:
    ///   - entityType: The entity type to compare
    ///   - context: The managed object context
    ///   - completion: Completion handler with comparison results or error
    private func continueCompareRecords(
        forEntityType entityType: String,
        in context: NSManagedObjectContext,
        completion: @escaping (ComparisonResult?, Error?) -> Void
    ) {
        // Fetch local records
        guard let localRecords = fetchLocalRecords(forEntityType: entityType, in: context) else {
            completion(nil, NSError(domain: "CloudKitDiagnostic", code: 1, userInfo: [
                NSLocalizedDescriptionKey: "Failed to fetch local records"
            ]))
            return
        }

        print("✅ Found \(localRecords.count) local records for \(entityType)")

        // Fetch cloud records
        fetchCloudRecords(forEntityType: entityType) { cloudRecords, error in
            if let error = error {
                print("❌ Error fetching cloud records: \(error.localizedDescription)")
                completion(nil, error)
                return
            }

            guard let cloudRecords = cloudRecords else {
                print("⚠️ No cloud records returned")
                completion(nil, NSError(domain: "CloudKitDiagnostic", code: 2, userInfo: [
                    NSLocalizedDescriptionKey: "No cloud records returned"
                ]))
                return
            }

            print("✅ Found \(cloudRecords.count) cloud records for \(entityType)")

            // Create the comparison result
            let result = ComparisonResult(
                entityType: entityType,
                localRecordCount: localRecords.count,
                cloudRecordCount: cloudRecords.count,
                localRecords: localRecords,
                cloudRecords: cloudRecords
            )

            completion(result, nil)
        }
    }

    /// Fetches a specific CloudKit record by ID
    /// - Parameters:
    ///   - recordID: The record ID to fetch
    ///   - completion: Completion handler with record or error
    func fetchRecord(
        withID recordID: CKRecord.ID,
        completion: @escaping (CKRecord?, Error?) -> Void
    ) {
        // Use the direct database method for iOS 15+
        privateDatabase.fetch(withRecordID: recordID) { record, error in
            if let error = error {
                print("Error fetching record \(recordID.recordName): \(error.localizedDescription)")
                completion(nil, error)
                return
            }

            completion(record, nil)
        }
    }

    /// Gets all available entity types
    /// - Returns: Array of entity type names
    func getEntityTypes() -> [String] {
        return entityTypes
    }

    /// Checks for records that exist in CloudKit but not locally
    /// - Parameter completion: Completion handler with array of entity types that have cloud-only records
    func checkForCloudOnlyRecords(completion: @escaping ([String], Error?) -> Void) {
        print("🔍 Checking for cloud-only records")

        // Get the managed object context from the shared persistence controller
        let context = PersistenceController.shared.container.viewContext

        // Create a dispatch group to wait for all entity types to be checked
        let group = DispatchGroup()

        // Track entity types with cloud-only records
        var entityTypesWithCloudOnlyRecords: [String] = []
        var overallError: Error?

        // Check each entity type
        for entityType in entityTypes {
            group.enter()

            compareRecords(forEntityType: entityType, in: context) { result, error in
                defer { group.leave() }

                if let error = error {
                    print("❌ Error comparing records for \(entityType): \(error.localizedDescription)")
                    overallError = error
                    return
                }

                guard let result = result else {
                    print("⚠️ No comparison result for \(entityType)")
                    return
                }

                // Check if there are cloud-only records
                let cloudOnlyCount = result.cloudRecordCount - result.syncedRecords.count
                if cloudOnlyCount > 0 {
                    print("⚠️ Found \(cloudOnlyCount) cloud-only records for \(entityType)")
                    entityTypesWithCloudOnlyRecords.append(entityType)
                } else {
                    print("✅ No cloud-only records for \(entityType)")
                }
            }
        }

        // Wait for all checks to complete
        group.notify(queue: .main) {
            print("📊 Cloud-only records check complete")
            print("📊 Entity types with cloud-only records: \(entityTypesWithCloudOnlyRecords.joined(separator: ", "))")

            completion(entityTypesWithCloudOnlyRecords, overallError)
        }
    }



    /// Checks if the Core Data CloudKit zone exists
    /// - Parameter completion: Completion handler with boolean indicating if zone exists
    func checkCoreDataCloudKitZoneExists(completion: @escaping (Bool) -> Void) {
        print("🔍 Checking if Core Data CloudKit zone exists")

        // Use the direct database method for iOS 15+
        privateDatabase.fetch(withRecordZoneID: coreDataCloudKitZoneID) { zone, error in
            if let error = error {
                if let ckError = error as? CKError, ckError.code == .zoneNotFound {
                    print("⚠️ Core Data CloudKit zone does not exist")
                    completion(false)
                } else {
                    print("❌ Error fetching record zone: \(error.localizedDescription)")
                    completion(false)
                }
                return
            }

            if zone != nil {
                print("✅ Core Data CloudKit zone exists")
                completion(true)
            } else {
                print("⚠️ Core Data CloudKit zone does not exist")
                completion(false)
            }
        }
    }

    /// Creates the Core Data CloudKit zone if it doesn't exist
    /// - Parameter completion: Completion handler with success flag
    func createCoreDataCloudKitZone(completion: @escaping (Bool) -> Void) {
        print("🔄 Attempting to create Core Data CloudKit zone")

        let zone = CKRecordZone(zoneID: coreDataCloudKitZoneID)

        // Use the direct database method for iOS 15+
        privateDatabase.save(zone) { savedZone, error in
            if let error = error {
                print("❌ Error creating Core Data CloudKit zone: \(error.localizedDescription)")
                completion(false)
                return
            }

            print("✅ Successfully created Core Data CloudKit zone")
            completion(true)
        }
    }
}

/// Represents the result of comparing local and cloud records
struct ComparisonResult {
    let entityType: String
    let localRecordCount: Int
    let cloudRecordCount: Int
    let localRecords: [NSManagedObject]
    let cloudRecords: [CKRecord]

    /// Records that exist both locally and in the cloud
    var syncedRecords: [(local: NSManagedObject, cloud: CKRecord)] {
        var result: [(local: NSManagedObject, cloud: CKRecord)] = []

        // Create dictionaries for faster lookup, handling empty IDs
        var localById: [String: NSManagedObject] = [:]
        for record in localRecords {
            // Check if the entity has an "id" property
            if entityHasIdProperty(record) {
                if let id = record.value(forKey: "id") as? String, !id.isEmpty {
                    localById[id] = record
                    #if DEBUG
                    if CloudKitDiagnosticManager.verboseLogging {
                        print("✅ Using 'id' field for local record: \(id)")
                    }
                    #endif

                    // Also add the raw objectID as a possible match key
                    let objectIdString = record.objectID.uriRepresentation().lastPathComponent
                    localById[objectIdString] = record
                } else {
                    // For records without a valid ID, use the objectID description as a fallback
                    let objectIdString = record.objectID.uriRepresentation().lastPathComponent

                    // Add both with and without the prefix
                    localById[objectIdString] = record
                    localById["local_\(objectIdString)"] = record

                    #if DEBUG
                    if CloudKitDiagnosticManager.verboseLogging {
                        print("⚠️ Using objectID as fallback for local record: \(objectIdString)")
                    }
                    #endif
                }
            } else {
                // For entities without an "id" property, use the objectID as the key
                let objectIdString = record.objectID.uriRepresentation().lastPathComponent

                // Add both with and without the prefix
                localById[objectIdString] = record
                localById["local_\(objectIdString)"] = record

                #if DEBUG
                if CloudKitDiagnosticManager.verboseLogging {
                    print("ℹ️ Entity has no 'id' property, using objectID: \(objectIdString)")
                }
                #endif
            }
        }

        // Helper function to check if an entity has an "id" property
        func entityHasIdProperty(_ record: NSManagedObject) -> Bool {
            let entity = record.entity
            return entity.attributesByName["id"] != nil
        }

        // Create a dictionary of cloud records by ID, handling potential duplicates
        var cloudById: [String: CKRecord] = [:]

        // Check if the entity type has an "id" property by looking at the first local record
        let hasIdProperty = !localRecords.isEmpty && entityHasIdProperty(localRecords[0])

        for record in cloudRecords {
            if hasIdProperty {
                // Check both prefixed and non-prefixed ID fields
                let regularId = record["id"] as? String
                let prefixedId = record["CD_id"] as? String

                // Use the first non-empty ID we find
                if let id = regularId, !id.isEmpty {
                    cloudById[id] = record
                    #if DEBUG
                    if CloudKitDiagnosticManager.verboseLogging {
                        print("✅ Using regular 'id' field for cloud record: \(id)")
                    }
                    #endif
                } else if let id = prefixedId, !id.isEmpty {
                    cloudById[id] = record
                    #if DEBUG
                    if CloudKitDiagnosticManager.verboseLogging {
                        print("✅ Using prefixed 'CD_id' field for cloud record: \(id)")
                    }
                    #endif
                } else {
                    // For records without a valid ID in either field, use the record name
                    let fallbackId = record.recordID.recordName
                    cloudById[fallbackId] = record
                    #if DEBUG
                    if CloudKitDiagnosticManager.verboseLogging {
                        print("⚠️ Using record name as fallback ID: \(fallbackId)")
                    }
                    #endif

                    // Also add with the local-style prefix to increase match chances
                    let localStyleId = "local_\(record.recordID.recordName)"
                    cloudById[localStyleId] = record
                }

                // Also add the record ID as a possible match key
                cloudById[record.recordID.recordName] = record
            } else {
                // For entities without an "id" property, use the record name as the key
                cloudById[record.recordID.recordName] = record

                // Also add with the local-style prefix to increase match chances
                let localStyleId = "local_\(record.recordID.recordName)"
                cloudById[localStyleId] = record
            }
        }

        // Find records that exist in both
        for (id, localRecord) in localById {
            if let cloudRecord = cloudById[id] {
                result.append((local: localRecord, cloud: cloudRecord))
            }
        }

        return result
    }

    /// Records that exist only locally
    var localOnlyRecords: [NSManagedObject] {
        // Helper function to check if an entity has an "id" property
        func entityHasIdProperty(_ record: NSManagedObject) -> Bool {
            let entity = record.entity
            return entity.attributesByName["id"] != nil
        }

        // Check if the entity type has an "id" property
        let hasIdProperty = !localRecords.isEmpty && entityHasIdProperty(localRecords[0])

        if hasIdProperty {
            // Create a set of cloud record IDs, including all possible matching keys
            var cloudIds = Set<String>()
            for record in cloudRecords {
                // Check both prefixed and non-prefixed ID fields
                if let id = record["id"] as? String, !id.isEmpty {
                    cloudIds.insert(id)
                }

                if let id = record["CD_id"] as? String, !id.isEmpty {
                    cloudIds.insert(id)
                }

                // Always add the record name as a possible match
                cloudIds.insert(record.recordID.recordName)
                cloudIds.insert("local_\(record.recordID.recordName)")
            }

            return localRecords.filter { record in
                // Check if the record has a valid ID
                if let id = record.value(forKey: "id") as? String, !id.isEmpty {
                    // If the ID is in the cloud IDs, it's not local-only
                    if cloudIds.contains(id) {
                        return false
                    }
                }

                // Also check if the objectID matches any cloud record name
                let objectIdString = record.objectID.uriRepresentation().lastPathComponent
                if cloudIds.contains(objectIdString) || cloudIds.contains("local_\(objectIdString)") {
                    return false
                }

                // If we get here, the record is local-only
                return true
            }
        } else {
            // For entities without an "id" property, use the objectID to match
            var cloudRecordNames = Set<String>()
            for record in cloudRecords {
                // Add the record name and variations
                cloudRecordNames.insert(record.recordID.recordName)
                cloudRecordNames.insert("local_\(record.recordID.recordName)")
            }

            return localRecords.filter { record in
                let recordName = record.objectID.uriRepresentation().lastPathComponent
                return !cloudRecordNames.contains(recordName) && !cloudRecordNames.contains("local_\(recordName)")
            }
        }
    }

    /// Records that exist only in the cloud
    var cloudOnlyRecords: [CKRecord] {
        // Helper function to check if an entity has an "id" property
        func entityHasIdProperty(_ record: NSManagedObject) -> Bool {
            let entity = record.entity
            return entity.attributesByName["id"] != nil
        }

        // Check if the entity type has an "id" property
        let hasIdProperty = !localRecords.isEmpty && entityHasIdProperty(localRecords[0])

        if hasIdProperty {
            // Create a set of local record IDs, including all possible matching keys
            var localIds = Set<String>()
            for record in localRecords {
                // Add the ID if it exists
                if let id = record.value(forKey: "id") as? String, !id.isEmpty {
                    localIds.insert(id)
                }

                // Always add the objectID as a possible match
                let objectIdString = record.objectID.uriRepresentation().lastPathComponent
                localIds.insert(objectIdString)
                localIds.insert("local_\(objectIdString)")
            }

            return cloudRecords.filter { record in
                // Check both prefixed and non-prefixed ID fields
                if let id = record["id"] as? String, !id.isEmpty {
                    if localIds.contains(id) {
                        return false
                    }
                }

                if let id = record["CD_id"] as? String, !id.isEmpty {
                    if localIds.contains(id) {
                        return false
                    }
                }

                // Also check if the record name matches any local objectID
                if localIds.contains(record.recordID.recordName) {
                    return false
                }

                // If we get here, the record is cloud-only
                return true
            }
        } else {
            // For entities without an "id" property, use the objectID to match
            var localRecordNames = Set<String>()
            for record in localRecords {
                let objectIdString = record.objectID.uriRepresentation().lastPathComponent
                localRecordNames.insert(objectIdString)
                localRecordNames.insert("local_\(objectIdString)")
            }

            return cloudRecords.filter { record in
                let recordName = record.recordID.recordName
                return !localRecordNames.contains(recordName) && !localRecordNames.contains("local_\(recordName)")
            }
        }
    }
}
