# Data Interoperability Implementation

This document provides instructions for implementing the data interoperability features in the PoleVaulterLog app.

## Required Dependencies

The implementation requires the ZIPFoundation package for creating ZIP archives. To add this package to your Xcode project:

1. In Xcode, go to File > Add Packages...
2. In the search bar, enter: `https://github.com/weichsel/ZIPFoundation.git`
3. Select the ZIPFoundation package and click "Add Package"
4. Choose the target "PoleVaulterLog" and click "Add Package"

## Implementation Details

The data interoperability features include:

1. **Export Single Session**:
   - JSON format (.pvl.json)
   - Excel format (.xlsx) - currently implemented as CSV

2. **Export All Sessions**:
   - ZIP archive containing:
     - Master JSON file (.pvl.json)
     - CSV file (.csv)
     - Excel file (.xlsx) - currently implemented as CSV

3. **Import**:
   - Import from .pvl.json files
   - Updates existing records or creates new ones

4. **Video Sharing**:
   - Share videos via iCloud links (PHSharingController)
   - Currently implemented as a placeholder

## Files Added

- `ExportImportModels.swift`: Codable models for JSON serialization
- `ExportImportManager.swift`: Core functionality for export/import
- `ExportSessionView.swift`: UI for exporting a single session
- `ExportAllView.swift`: UI for exporting all sessions
- `ImportDataView.swift`: UI for importing data
- `VideoSharingView.swift`: UI for sharing videos
- `SessionListForExport.swift`: UI component for listing sessions to export

## Future Improvements

1. Replace CSV export with proper Excel (.xlsx) export using a library like XlsxWriter
2. Implement actual video sharing using PHSharingController
3. Add progress indicators for large exports/imports
4. Add filtering options for exports
5. Implement incremental backups
