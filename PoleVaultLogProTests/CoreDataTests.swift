import XCTest
@testable import PoleVaulterLog
import CoreData

final class CoreDataTests: XCTestCase {
    
    var persistenceController: PersistenceController!
    var context: NSManagedObjectContext!
    
    override func setUpWithError() throws {
        // Set up an in-memory Core Data stack for testing
        persistenceController = PersistenceController(inMemory: true)
        context = persistenceController.container.viewContext
    }
    
    override func tearDownWithError() throws {
        // Clean up the context
        context = nil
        persistenceController = nil
    }
    
    func testCreateAthlete() throws {
        // Create a test athlete
        let athlete = Athlete.create(in: context, name: "Test Athlete", dateOfBirth: Date(), dominantHand: "right")
        
        // Save the context
        try context.save()
        
        // Verify the athlete was created with the correct properties
        XCTAssertNotNil(athlete.id)
        XCTAssertEqual(athlete.name, "Test Athlete")
        XCTAssertEqual(athlete.dominantHand, "right")
        XCTAssertEqual(athlete.personalBestCm, 0)
        XCTAssertEqual(athlete.personalBestIn, 0)
    }
    
    func testCreateSession() throws {
        // Create a test athlete
        let athlete = Athlete.create(in: context, name: "Test Athlete")
        
        // Create a test session
        let session = Session.create(in: context, 
                                    athlete: athlete, 
                                    type: "practice", 
                                    location: "Test Location", 
                                    weather: "Sunny")
        
        // Save the context
        try context.save()
        
        // Verify the session was created with the correct properties
        XCTAssertNotNil(session.id)
        XCTAssertEqual(session.type, "practice")
        XCTAssertEqual(session.location, "Test Location")
        XCTAssertEqual(session.weather, "Sunny")
        XCTAssertTrue(session.title?.contains("Practice") ?? false)
        XCTAssertEqual(session.athlete, athlete)
    }
    
    func testCreateJump() throws {
        // Create a test athlete and session
        let athlete = Athlete.create(in: context, name: "Test Athlete")
        let session = Session.create(in: context, athlete: athlete, type: "practice")
        
        // Create a test jump
        let jump = Jump.create(in: context, 
                              session: session, 
                              order: 1, 
                              heightCm: 400.0, 
                              result: "make", 
                              comment: "Good clearance")
        
        // Save the context
        try context.save()
        
        // Verify the jump was created with the correct properties
        XCTAssertNotNil(jump.id)
        XCTAssertEqual(jump.order, 1)
        XCTAssertEqual(jump.barHeightCm, 400.0)
        XCTAssertEqual(jump.barHeightIn, 400.0 / 2.54)
        XCTAssertEqual(jump.result, "make")
        XCTAssertEqual(jump.comment, "Good clearance")
        XCTAssertEqual(jump.session, session)
        
        // Verify the session's best height was updated
        XCTAssertEqual(session.bestHeightCm, 400.0)
        XCTAssertEqual(session.bestHeightIn, 400.0 / 2.54)
        
        // Verify the athlete's personal best was updated
        XCTAssertEqual(athlete.personalBestCm, 400.0)
        XCTAssertEqual(athlete.personalBestIn, 400.0 / 2.54)
    }
    
    func testToggleJumpResult() throws {
        // Create a test athlete and session
        let athlete = Athlete.create(in: context, name: "Test Athlete")
        let session = Session.create(in: context, athlete: athlete, type: "practice")
        
        // Create a test jump with "miss" result
        let jump = Jump.create(in: context, 
                              session: session, 
                              order: 1, 
                              heightCm: 400.0, 
                              result: "miss")
        
        // Save the context
        try context.save()
        
        // Verify initial state
        XCTAssertEqual(jump.result, "miss")
        XCTAssertEqual(session.bestHeightCm, 0)
        XCTAssertEqual(athlete.personalBestCm, 0)
        
        // Toggle the result to "make"
        let newResult = jump.toggleResult()
        try context.save()
        
        // Verify the result was toggled and best heights were updated
        XCTAssertEqual(newResult, "make")
        XCTAssertEqual(jump.result, "make")
        XCTAssertEqual(session.bestHeightCm, 400.0)
        XCTAssertEqual(athlete.personalBestCm, 400.0)
        
        // Toggle back to "miss"
        _ = jump.toggleResult()
        try context.save()
        
        // Verify the result was toggled (best heights should remain unchanged)
        XCTAssertEqual(jump.result, "miss")
        XCTAssertEqual(session.bestHeightCm, 400.0)
        XCTAssertEqual(athlete.personalBestCm, 400.0)
    }
    
    func testMarkJumpAsPass() throws {
        // Create a test athlete and session
        let athlete = Athlete.create(in: context, name: "Test Athlete")
        let session = Session.create(in: context, athlete: athlete, type: "practice")
        
        // Create a test jump with "miss" result
        let jump = Jump.create(in: context, 
                              session: session, 
                              order: 1, 
                              heightCm: 400.0, 
                              result: "miss")
        
        // Mark the jump as a pass
        jump.markAsPass()
        try context.save()
        
        // Verify the result was changed to "pass"
        XCTAssertEqual(jump.result, "pass")
    }
    
    func testAddVideoToSession() throws {
        // Create a test athlete and session
        let athlete = Athlete.create(in: context, name: "Test Athlete")
        let session = Session.create(in: context, athlete: athlete, type: "practice")
        
        // Add a video to the session
        let videoId = "test-video-id"
        session.addVideo(localId: videoId)
        try context.save()
        
        // Verify the video was added to the session
        let videos = session.videoLocalIds as? [String] ?? []
        XCTAssertEqual(videos.count, 1)
        XCTAssertEqual(videos.first, videoId)
        
        // Add the same video again (should not duplicate)
        session.addVideo(localId: videoId)
        try context.save()
        
        // Verify the video was not duplicated
        let updatedVideos = session.videoLocalIds as? [String] ?? []
        XCTAssertEqual(updatedVideos.count, 1)
    }
}
