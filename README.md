# PoleVaultLogPro

A fast, sleek, and delight-to-use logbook for pole vaulters that captures every jump, note, and video while remaining 100% user-controlled and easily shareable.

## Core Data Model

The app uses Core Data with CloudKit integration for persistence and synchronization across devices.

### Entities

#### Athlete
- `id` (UUID): Unique identifier
- `name` (String): Athlete's name
- `dateOfBirth` (Date): Athlete's date of birth
- `dominantHand` (String): Athlete's dominant hand (left/right)
- `personalBestCm` (Double): Personal best height in centimeters
- `personalBestIn` (Double): Personal best height in inches
- Relationship: One-to-many with Session

#### Session
- `id` (UUID): Unique identifier
- `date` (Date): Date of the session
- `type` (String): Type of session (practice/meet)
- `title` (String): Title of the session
- `location` (String): Location of the session
- `weather` (String): Weather conditions
- `notesAthlete` (String): Notes from the athlete
- `notesCoach` (String): Notes from the coach
- `bestHeightCm` (Double): Best height achieved in centimeters
- `bestHeightIn` (Double): Best height achieved in inches
- `videoLocalIds` ([String]): Array of video local identifiers
- Relationships: Many-to-one with Athlete, one-to-many with Jump

#### Jump
- `id` (UUID): Unique identifier
- `order` (Int16): Order of the jump within the session
- `barHeightCm` (Double): Bar height in centimeters
- `barHeightIn` (Double): Bar height in inches
- `result` (String): Result of the jump (make/miss/pass)
- `comment` (String): Comment about the jump
- `videoLocalId` (String): Local identifier of the associated video
- Relationship: Many-to-one with Session

## Implementation Notes

- Heights are stored in both centimeters and inches for efficient querying and display
- Videos are stored as PHAsset local identifiers, not as files within the app
- CloudKit sync is enabled for multi-device access
- Lightweight migrations are supported for schema evolution

## Build Sequence

1. ✅ Core Data schema + CloudKit setup
2. ✅ SwiftUI navigation structure (TabView)
3. ✅ Session flow MVP
4. Video integration
5. ✅ Analytics dashboard
6. Export/import pipeline
7. ✅ Biometric lock UI
8. Polish and refinements
